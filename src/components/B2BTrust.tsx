import React from 'react';
import { cn } from '@/lib/utils';
import FadeInSection from './FadeInSection';
import Image from 'next/image';

const achievements = [
  {
    metric: '80+',
    label: 'B2B Companies',
    description: 'CRM implementations completed',
    icon: '🏢'
  },
  {
    metric: '10+',
    label: 'Years Experience',
    description: 'In CRM and sales automation',
    icon: '⏰'
  },
  {
    metric: '320+',
    label: 'Projects Completed',
    description: 'Across all industries',
    icon: '💰'
  },
  {
    metric: '98%',
    label: 'Client Satisfaction',
    description: 'Based on project reviews',
    icon: '⭐'
  }
];

const platformCertifications = [
  {
    name: 'Kommo Certified Partner',
    description: 'Official CRM implementation partner',
    icon: '🏆',
    color: 'bg-green-100 text-green-600'
  },
  {
    name: 'Make.com Expert',
    description: 'Advanced automation and integrations',
    icon: '🔗',
    color: 'bg-orange-100 text-orange-600'
  },
  {
    name: 'B2B CRM Specialist',
    description: 'Complex sales process automation',
    icon: '📊',
    color: 'bg-blue-100 text-blue-600'
  },
  {
    name: 'Integration Expert',
    description: 'API connections and workflow automation',
    icon: '🔧',
    color: 'bg-purple-100 text-purple-600'
  }
];

const industryLogos = [
  { name: 'Industrial Solutions Inc', logo: '🏭' },
  { name: 'MedTech Innovations', logo: '⚕️' },
  { name: 'Scientific Equipment Co', logo: '🔬' },
  { name: 'Construction Materials Pro', logo: '🏗️' },
  { name: 'Automotive Parts Direct', logo: '🚗' },
  { name: 'Energy Solutions Group', logo: '⚡' }
];

const certifications = [
  {
    name: 'B2B Sales Process Expert',
    description: 'Specialized in complex B2B sales cycle automation',
    icon: '🎯',
    color: 'bg-blue-100 text-blue-600'
  },
  {
    name: 'CRM Integration Specialist',
    description: 'Connecting CRM with business systems via Make.com',
    icon: '🔗',
    color: 'bg-purple-100 text-purple-600'
  },
  {
    name: 'Customer Lifecycle Management',
    description: 'From lead to retention - complete journey automation',
    icon: '🔄',
    color: 'bg-gray-100 text-gray-600'
  },
  {
    name: 'Sales Team Training',
    description: 'Training teams on CRM best practices and workflows',
    icon: '👥',
    color: 'bg-indigo-100 text-indigo-600'
  }
];

const EcommerceTrust: React.FC = () => {
  return (
    <div className="space-y-16">
      {/* Certifications Section */}
      <FadeInSection>
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Certified B2B CRM Experts
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Trust your complex B2B sales automation to certified professionals with proven expertise in enterprise solutions
          </p>
        </div>
      </FadeInSection>

      {/* Main Certification Badge */}
      <FadeInSection delay={200}>
        <div className="text-center mb-12">
          <div className="inline-block bg-white rounded-lg p-6 shadow-xl max-w-3xl mx-auto">
            <img
              src="/Crtifikat.webp"
              alt="Kommo Partnership Certificate"
              className="w-full max-w-2xl mx-auto rounded-lg border border-gray-200"
            />
          </div>
        </div>
      </FadeInSection>


      {/* Achievements Section */}
      <FadeInSection delay={800}>
        <div className="bg-gradient-to-br from-gray-900 to-blue-900 rounded-2xl p-8 md:p-12 text-white">
          <div className="text-center mb-12">
            <h3 className="text-2xl md:text-3xl font-bold mb-4">
              Proven Track Record in B2B
            </h3>
            <p className="text-blue-200 max-w-2xl mx-auto">
              Our experience spans across industries and complex B2B sales environments, 
              delivering measurable results for specialized businesses
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {achievements.map((achievement, index) => (
              <FadeInSection key={index} delay={1000 + index * 150}>
                <div className="text-center">
                  <div className="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center text-2xl mx-auto mb-4">
                    {achievement.icon}
                  </div>
                  <div className="text-3xl md:text-4xl font-bold text-blue-400 mb-2">
                    {achievement.metric}
                  </div>
                  <div className="text-lg font-semibold mb-2">
                    {achievement.label}
                  </div>
                  <div className="text-blue-200 text-sm">
                    {achievement.description}
                  </div>
                </div>
              </FadeInSection>
            ))}
          </div>
        </div>
      </FadeInSection>

      {/* Industry Expertise */}
      <FadeInSection delay={1200}>
        <div className="bg-white rounded-2xl p-8 shadow-lg border border-gray-100">
          <div className="text-center mb-8">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              Specialized Industry Expertise
            </h3>
            <p className="text-gray-600">
              Deep knowledge across various B2B and niche market segments
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {certifications.map((cert, index) => (
              <FadeInSection key={index} delay={1400 + index * 150}>
                <div className="text-center p-6 rounded-xl border border-gray-100 hover:shadow-lg transition-shadow duration-300">
                  <div className={cn(
                    'w-16 h-16 rounded-full flex items-center justify-center text-2xl mx-auto mb-4',
                    cert.color
                  )}>
                    {cert.icon}
                  </div>
                  <h4 className="font-semibold text-gray-900 mb-2">
                    {cert.name}
                  </h4>
                  <p className="text-gray-600 text-sm">
                    {cert.description}
                  </p>
                </div>
              </FadeInSection>
            ))}
          </div>
        </div>
      </FadeInSection>

      {/* B2B Methodology */}
      <FadeInSection delay={2200}>
        <div className="bg-white rounded-2xl p-8 border border-gray-100 shadow-lg">
          <div className="text-center mb-8">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              Our B2B Methodology
            </h3>
            <p className="text-gray-600">
              Specialized approach designed for complex B2B sales cycles and market requirements
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="text-center p-4">
              <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 text-xl mx-auto mb-3">
                🎯
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">Multi-Stakeholder Focus</h4>
              <p className="text-gray-600 text-sm">Handle complex decision-making processes with multiple buyers</p>
            </div>
            
            <div className="text-center p-4">
              <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center text-purple-600 text-xl mx-auto mb-3">
                🔧
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">Custom Solutions</h4>
              <p className="text-gray-600 text-sm">Tailored automation for unique industry requirements</p>
            </div>
            
            <div className="text-center p-4">
              <div className="w-12 h-12 bg-indigo-100 rounded-full flex items-center justify-center text-indigo-600 text-xl mx-auto mb-3">
                📊
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">Data-Driven Insights</h4>
              <p className="text-gray-600 text-sm">Advanced analytics for complex B2B sales metrics</p>
            </div>
            
            <div className="text-center p-4">
              <div className="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center text-orange-600 text-xl mx-auto mb-3">
                🔄
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">Long-term Partnerships</h4>
              <p className="text-gray-600 text-sm">Ongoing support for evolving business needs</p>
            </div>
          </div>
        </div>
      </FadeInSection>


    </div>
  );
};

export default EcommerceTrust;
