import React from 'react';
import { cn } from '@/lib/utils';
import FadeInSection from './FadeInSection';

interface FunnelStage {
  id: number;
  title: string;
  description: string;
  icon: string;
  automation: string[];
  color: string;
  percentage: number;
}

const funnelStages: FunnelStage[] = [
  {
    id: 1,
    title: 'Lead Generation & Discovery',
    description: 'Attract B2B decision makers and key stakeholders',
    icon: '🎯',
    automation: [
      'Multi-channel lead capture (website, trade shows, referrals)',
      'Lead scoring based on company size and buying signals',
      'Automated qualification questionnaires'
    ],
    color: 'from-blue-500 to-blue-600',
    percentage: 100
  },
  {
    id: 2,
    title: 'Qualification & Needs Assessment',
    description: 'Identify decision makers and specific requirements',
    icon: '🔍',
    automation: [
      'Automated discovery call scheduling',
      'Pre-call research and company profiling',
      'Custom needs assessment forms'
    ],
    color: 'from-indigo-500 to-indigo-600',
    percentage: 45
  },
  {
    id: 3,
    title: 'Product Demo & Presentation',
    description: 'Showcase solutions tailored to client needs',
    icon: '📊',
    automation: [
      'Personalized demo scheduling and preparation',
      'Automated follow-up sequences post-demo',
      'Custom proposal generation based on requirements'
    ],
    color: 'from-purple-500 to-purple-600',
    percentage: 30
  },
  {
    id: 4,
    title: 'Proposal & Negotiation',
    description: 'Present pricing and negotiate terms',
    icon: '💼',
    automation: [
      'Proposal tracking and follow-up automation',
      'Deal stage progression and notifications',
      'Negotiation history and approval workflows'
    ],
    color: 'from-pink-500 to-pink-600',
    percentage: 20
  },
  {
    id: 5,
    title: 'Order Processing & Onboarding',
    description: 'Convert prospects into paying customers',
    icon: '✅',
    automation: [
      'Deal closing notifications and handoff',
      'Customer onboarding task automation',
      'Account setup reminders and follow-up'
    ],
    color: 'from-red-500 to-red-600',
    percentage: 12
  },
  {
    id: 6,
    title: 'Customer Success & Retention',
    description: 'Ensure satisfaction and drive repeat business',
    icon: '🚀',
    automation: [
      'Regular check-ins and satisfaction surveys',
      'Upselling and cross-selling opportunities',
      'Renewal reminders and loyalty programs'
    ],
    color: 'from-orange-500 to-orange-600',
    percentage: 10
  }
];

const B2BFunnel: React.FC = () => {
  return (
    <div className="space-y-12">
      <FadeInSection>
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            B2B Sales Process with Kommo CRM Automation
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            See how Kommo CRM automates complex B2B sales cycles and business relationships,
            from lead generation to long-term partnership success
          </p>
        </div>
      </FadeInSection>

      {/* Funnel Visualization */}
      <div className="relative">
        {/* Connection Lines */}
        <div className="hidden lg:block absolute left-1/2 top-0 bottom-0 w-0.5 bg-gradient-to-b from-blue-200 via-purple-200 to-orange-200 transform -translate-x-1/2"></div>

        <div className="space-y-8">
          {funnelStages.map((stage, index) => (
            <FadeInSection key={stage.id} delay={index * 200}>
              <div className={cn(
                'relative flex flex-col lg:flex-row items-center gap-8',
                index % 2 === 0 ? 'lg:flex-row' : 'lg:flex-row-reverse'
              )}>
                {/* Stage Content */}
                <div className="flex-1 max-w-lg">
                  <div className="bg-white rounded-xl shadow-lg p-6 border border-gray-100 hover:shadow-xl transition-shadow duration-300">
                    <div className="flex items-center mb-4">
                      <div className={cn(
                        'w-12 h-12 rounded-full flex items-center justify-center text-2xl text-white mr-4',
                        `bg-gradient-to-r ${stage.color}`
                      )}>
                        {stage.icon}
                      </div>
                      <div>
                        <h3 className="text-xl font-bold text-gray-900">{stage.title}</h3>
                        <p className="text-gray-600 text-sm">{stage.description}</p>
                      </div>
                    </div>

                    <div className="space-y-3">
                      <h4 className="font-semibold text-gray-800 text-sm uppercase tracking-wide">
                        Automation Features:
                      </h4>
                      <ul className="space-y-2">
                        {stage.automation.map((feature, featureIndex) => (
                          <li key={featureIndex} className="flex items-start text-sm text-gray-700">
                            <span className="text-blue-500 mr-2 mt-0.5">✓</span>
                            <span>{feature}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>

                {/* Stage Number & Percentage */}
                <div className="flex-shrink-0">
                  <div className="relative">
                    <div className={cn(
                      'w-20 h-20 rounded-full flex items-center justify-center text-white font-bold text-lg shadow-lg',
                      `bg-gradient-to-r ${stage.color}`
                    )}>
                      {stage.id}
                    </div>
                    <div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 text-center">
                      <div className="text-sm font-semibold text-gray-600">
                        {stage.percentage}%
                      </div>
                      <div className="text-xs text-gray-500">conversion</div>
                    </div>
                  </div>
                </div>

                {/* Funnel Bar */}
                <div className="flex-1 max-w-lg">
                  <div className="bg-gray-100 rounded-full h-8 overflow-hidden">
                    <div 
                      className={cn(
                        'h-full rounded-full transition-all duration-1000 ease-out',
                        `bg-gradient-to-r ${stage.color}`
                      )}
                      style={{ width: `${stage.percentage}%` }}
                    ></div>
                  </div>
                  <div className="mt-2 text-center">
                    <span className="text-sm font-medium text-gray-700">
                      {stage.percentage}% of leads reach this stage
                    </span>
                  </div>
                </div>
              </div>
            </FadeInSection>
          ))}
        </div>
      </div>

      {/* Key Benefits */}
      <FadeInSection delay={1200}>
        <div className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-2xl p-8 mt-16">
          <div className="text-center mb-8">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              Why This Funnel Works for B2B Companies
            </h3>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center text-2xl text-blue-600 mx-auto mb-4">
                🎯
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">Complex Sales Cycles</h4>
              <p className="text-gray-600 text-sm">
                Handles longer decision-making processes with multiple stakeholders and touchpoints
              </p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center text-2xl text-purple-600 mx-auto mb-4">
                🔧
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">Customization Focus</h4>
              <p className="text-gray-600 text-sm">
                Tailored solutions and personalized approaches for enterprise requirements
              </p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-indigo-100 rounded-full flex items-center justify-center text-2xl text-indigo-600 mx-auto mb-4">
                📈
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">Relationship Building</h4>
              <p className="text-gray-600 text-sm">
                Long-term partnerships and repeat business through systematic customer success
              </p>
            </div>
          </div>
        </div>
      </FadeInSection>

      {/* B2B vs B2C Comparison */}
      <FadeInSection delay={1400}>
        <div className="bg-white rounded-2xl p-8 border border-gray-100 shadow-lg">
          <div className="text-center mb-8">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              B2B vs Traditional B2C Sales Metrics
            </h3>
            <p className="text-gray-600">
              Understanding the unique characteristics of B2B sales processes
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className="space-y-4">
              <h4 className="text-lg font-semibold text-gray-900 mb-4">B2B Sales Characteristics</h4>
              <div className="space-y-3">
                <div className="flex justify-between items-center p-3 bg-blue-50 rounded-lg">
                  <span className="text-gray-700">Average Deal Size</span>
                  <span className="font-semibold text-blue-600">$5,000 - $50,000+</span>
                </div>
                <div className="flex justify-between items-center p-3 bg-blue-50 rounded-lg">
                  <span className="text-gray-700">Sales Cycle Length</span>
                  <span className="font-semibold text-blue-600">3-12 months</span>
                </div>
                <div className="flex justify-between items-center p-3 bg-blue-50 rounded-lg">
                  <span className="text-gray-700">Decision Makers</span>
                  <span className="font-semibold text-blue-600">3-8 people</span>
                </div>
                <div className="flex justify-between items-center p-3 bg-blue-50 rounded-lg">
                  <span className="text-gray-700">Customer Lifetime Value</span>
                  <span className="font-semibold text-blue-600">$50,000 - $500,000+</span>
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <h4 className="text-lg font-semibold text-gray-900 mb-4">B2B Industry Focus Areas</h4>
              <div className="space-y-3">
                <div className="flex items-center p-3 bg-purple-50 rounded-lg">
                  <span className="text-purple-500 mr-3">🏭</span>
                  <span className="text-gray-700">Industrial Equipment & Supplies</span>
                </div>
                <div className="flex items-center p-3 bg-purple-50 rounded-lg">
                  <span className="text-purple-500 mr-3">⚕️</span>
                  <span className="text-gray-700">Medical & Healthcare Products</span>
                </div>
                <div className="flex items-center p-3 bg-purple-50 rounded-lg">
                  <span className="text-purple-500 mr-3">🔬</span>
                  <span className="text-gray-700">Scientific & Laboratory Equipment</span>
                </div>
                <div className="flex items-center p-3 bg-purple-50 rounded-lg">
                  <span className="text-purple-500 mr-3">🏗️</span>
                  <span className="text-gray-700">Construction & Building Materials</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </FadeInSection>

      {/* Success Metrics */}
      <FadeInSection delay={1600}>
        <div className="bg-gradient-to-r from-gray-900 to-blue-900 rounded-2xl p-8 text-white">
          <div className="text-center mb-8">
            <h3 className="text-2xl font-bold mb-4">
              Typical B2B Sales Funnel Performance
            </h3>
            <p className="text-blue-100">
              Industry benchmarks for B2B sales funnels and conversion rates
            </p>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            <div className="text-center">
              <div className="text-3xl font-bold text-blue-400 mb-2">45%</div>
              <div className="text-sm text-blue-200">Lead to Qualified Prospect</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-purple-400 mb-2">30%</div>
              <div className="text-sm text-blue-200">Demo to Proposal</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-pink-400 mb-2">20%</div>
              <div className="text-sm text-blue-200">Proposal to Negotiation</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-orange-400 mb-2">12%</div>
              <div className="text-sm text-blue-200">Overall Conversion Rate</div>
            </div>
          </div>
        </div>
      </FadeInSection>
    </div>
  );
};

export default B2BFunnel;
