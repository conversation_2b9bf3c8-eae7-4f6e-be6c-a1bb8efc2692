'use client';

import React from 'react';
import Image from 'next/image';
import FadeInSection from './FadeInSection';
import { cn } from '@/lib/utils';

const partners = [
  { name: 'Kommo Certified Partner', logo: '/kommo-partner.webp', verified: true },
  { name: 'QuickBooks Integration Expert', logo: '💰', verified: true },
  { name: 'Calendly Integration Partner', logo: '📅', verified: true },
  { name: 'Make.com Integration Expert', logo: '🔗', verified: true }
];

const achievements = [
  {
    metric: '320+',
    label: 'Construction Projects',
    description: 'Successfully implemented CRM solutions',
    icon: '🚀'
  },
  {
    metric: '10+',
    label: 'Years Experience',
    description: 'In construction technology solutions',
    icon: '⭐'
  },
  {
    metric: '4000+',
    label: 'Contractors Trained',
    description: 'On Kommo CRM best practices',
    icon: '👥'
  },
  {
    metric: '98%',
    label: 'Client Satisfaction',
    description: 'Based on post-implementation surveys',
    icon: '💯'
  }
];

const clientLogos = [
  { name: 'BuildRight Construction', logo: '🏗️' },
  { name: 'Premier Contractors LLC', logo: '⭐' },
  { name: 'Wilson Construction Group', logo: '🚀' },
  { name: 'Elite Building Solutions', logo: '💼' },
  { name: 'Modern Construction Co', logo: '🏢' },
  { name: 'Reliable Contractors Inc', logo: '🔧' }
];

const certifications = [
  {
    name: 'Kommo CRM Expert',
    description: 'Advanced CRM implementation and optimization',
    icon: '🎯',
    color: 'bg-blue-100 text-blue-600'
  },
  {
    name: 'QuickBooks Integration Specialist',
    description: 'Expert-level accounting and financial automation',
    icon: '💰',
    color: 'bg-green-100 text-green-600'
  },
  {
    name: 'Calendly Integration Certified',
    description: 'Scheduling and appointment automation expertise',
    icon: '📅',
    color: 'bg-purple-100 text-purple-600'
  },
  {
    name: 'Make.com Partner',
    description: 'Advanced automation and integration solutions',
    icon: '🔗',
    color: 'bg-orange-100 text-orange-600'
  }
];

export default function ConstructionTrust() {
  return (
    <section className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Certifications Section */}
        <FadeInSection>
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Certified Construction CRM Experts
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Trust your construction CRM implementation to certified professionals with proven expertise
            </p>
          </div>
        </FadeInSection>

        {/* Main Certification Badge */}
        <FadeInSection delay={200}>
          <div className="text-center mb-12">
            <div className="inline-block bg-white rounded-lg p-6 shadow-xl max-w-3xl mx-auto">
              <img
                src="/Crtifikat.webp"
                alt="Kommo Partnership Certificate"
                className="w-full max-w-2xl mx-auto rounded-lg border border-gray-200"
              />
            </div>
          </div>
        </FadeInSection>



        {/* Certifications */}
        
      </div>
    </section>
  );
}
