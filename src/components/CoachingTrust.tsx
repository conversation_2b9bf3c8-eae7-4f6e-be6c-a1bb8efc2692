import React from 'react';
import { cn } from '@/lib/utils';
import FadeInSection from './FadeInSection';
import Image from 'next/image';

const partners = [
  { name: 'Kommo Certified Partner', logo: '/Crtifikat.webp', verified: true },
  { name: 'ICF Certified Coach', logo: '🏆', verified: true },
  { name: 'Teachable Integration Expert', logo: '📚', verified: true },
  { name: 'Make.com Automation Partner', logo: '🔗', verified: true }
];

const achievements = [
  {
    metric: '800+',
    label: 'Coaches Supported',
    description: 'Successfully implemented CRM solutions',
    icon: '👥'
  },
  {
    metric: '10+',
    label: 'Years Experience',
    description: 'In coaching and education technology',
    icon: '⏰'
  },
  {
    metric: '2000+',
    label: 'Clients Managed',
    description: 'Through our CRM systems',
    icon: '🎓'
  },
  {
    metric: '96%',
    label: 'Client Satisfaction',
    description: 'Based on post-implementation surveys',
    icon: '⭐'
  }
];

const clientLogos = [
  { name: 'Life Transformation Academy', logo: '🌟' },
  { name: 'Business Growth Coaching', logo: '📈' },
  { name: 'Mindfulness Masters', logo: '🧘' },
  { name: 'Career Success Hub', logo: '💼' },
  { name: 'Health & Wellness Coaches', logo: '💚' },
  { name: 'Leadership Development Pro', logo: '👑' }
];


const CoachingTrust: React.FC = () => {
  return (
    <div className="space-y-16">
      {/* Certifications Section */}
      <FadeInSection>
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Certified Coaching & Education CRM Experts
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Trust your coaching business CRM implementation to certified professionals with proven expertise
          </p>
        </div>
      </FadeInSection>

      {/* Main Certification Badge */}
      <FadeInSection delay={200}>
        <div className="text-center mb-12">
          <div className="inline-block bg-white rounded-lg p-6 shadow-xl max-w-3xl mx-auto">
            <img
              src="/Crtifikat.webp"
              alt="Kommo Partnership Certificate"
              className="w-full max-w-2xl mx-auto rounded-lg border border-gray-200"
            />
          </div>
        </div>
      </FadeInSection>



      {/* Coaching Methodology */}
      <FadeInSection delay={2000}>
        <div className="bg-white rounded-2xl p-8 border border-gray-100 shadow-lg">
          <div className="text-center mb-8">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              Our Coaching-Specific Methodology
            </h3>
            <p className="text-gray-600">
              We understand the unique needs of coaches and consultants
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="text-center p-4">
              <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center text-green-600 text-xl mx-auto mb-3">
                🎯
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">Goal-Oriented</h4>
              <p className="text-gray-600 text-sm">Focus on client transformation and measurable outcomes</p>
            </div>
            
            <div className="text-center p-4">
              <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 text-xl mx-auto mb-3">
                💝
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">Relationship-First</h4>
              <p className="text-gray-600 text-sm">Build trust and long-term client relationships</p>
            </div>
            
            <div className="text-center p-4">
              <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center text-purple-600 text-xl mx-auto mb-3">
                📈
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">Client Tracking</h4>
              <p className="text-gray-600 text-sm">Monitor client relationships and celebrate milestones</p>
            </div>
            
            <div className="text-center p-4">
              <div className="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center text-orange-600 text-xl mx-auto mb-3">
                🔄
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">Continuous Support</h4>
              <p className="text-gray-600 text-sm">Ongoing engagement and value delivery</p>
            </div>
          </div>
        </div>
      </FadeInSection>
    </div>
  );
};

export default CoachingTrust;
