import React from 'react';
import { cn } from '@/lib/utils';
import FadeInSection from './FadeInSection';
import Image from 'next/image';

const achievements = [
  {
    metric: '50+',
    label: 'SaaS Companies',
    description: 'CRM implementations completed',
    icon: '🚀'
  },
  {
    metric: '10+',
    label: 'Years Experience',
    description: 'In CRM and automation',
    icon: '⏰'
  },
  {
    metric: '320+',
    label: 'Projects Completed',
    description: 'Across all industries',
    icon: '💰'
  },
  {
    metric: '98%',
    label: 'Client Satisfaction',
    description: 'Based on project reviews',
    icon: '⭐'
  }
];

const techCertifications = [
  {
    name: 'Kommo Certified Partner',
    description: 'Official CRM implementation partner',
    icon: '🏆',
    color: 'bg-orange-100 text-orange-600'
  },
  {
    name: 'Make.com Expert',
    description: 'Advanced automation and integrations',
    icon: '🔗',
    color: 'bg-blue-100 text-blue-600'
  },
  {
    name: 'CRM Specialist',
    description: 'Sales process optimization expert',
    icon: '📊',
    color: 'bg-cyan-100 text-cyan-600'
  },
  {
    name: 'Integration Expert',
    description: 'API connections and workflow automation',
    icon: '🔧',
    color: 'bg-purple-100 text-purple-600'
  }
];

const saasLogos = [
  { name: 'CloudSync Pro', logo: '☁️' },
  { name: 'DevTools Analytics', logo: '📊' },
  { name: 'API Gateway Solutions', logo: '🔗' },
  { name: 'SecureAuth Platform', logo: '🔒' },
  { name: 'DataFlow Systems', logo: '📈' },
  { name: 'CodeBase Manager', logo: '💻' }
];

const techSpecializations = [
  {
    name: 'SaaS Sales Process Expert',
    description: 'Specialized in SaaS sales cycle automation and optimization',
    icon: '🎯',
    color: 'bg-cyan-100 text-cyan-600'
  },
  {
    name: 'Customer Lifecycle Management',
    description: 'From trial to renewal - complete customer journey automation',
    icon: '🔄',
    color: 'bg-blue-100 text-blue-600'
  },
  {
    name: 'CRM Integration Specialist',
    description: 'Connecting CRM with tech stack and third-party tools',
    icon: '🔗',
    color: 'bg-indigo-100 text-indigo-600'
  },
  {
    name: 'Sales Team Training',
    description: 'Training sales teams on CRM best practices and workflows',
    icon: '👥',
    color: 'bg-purple-100 text-purple-600'
  }
];

const SaasTrust: React.FC = () => {
  return (
    <div className="space-y-16">
      {/* Certifications Section */}
      <FadeInSection>
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Certified SaaS & Tech CRM Specialists
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Trust your product-led growth and tech sales automation to certified experts with deep SaaS industry knowledge
          </p>
        </div>
      </FadeInSection>

      {/* Main Certification Badge */}
      <FadeInSection delay={200}>
        <div className="text-center mb-12">
          <div className="inline-block bg-white rounded-lg p-6 shadow-xl max-w-3xl mx-auto">
            <img
              src="/Crtifikat.webp"
              alt="Kommo Partnership Certificate"
              className="w-full max-w-2xl mx-auto rounded-lg border border-gray-200"
            />
          </div>
        </div>
      </FadeInSection>

      {/* Tech Platform Certifications */}
      <FadeInSection delay={400}>
        <div className="bg-white rounded-2xl p-8 shadow-lg border border-gray-100">
          <div className="text-center mb-8">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              Technology Platform Certifications & Integrations
            </h3>
            <p className="text-gray-600">
              We're certified experts in the platforms that power modern SaaS and tech companies
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {techCertifications.map((cert, index) => (
              <FadeInSection key={index} delay={600 + index * 150}>
                <div className="text-center p-6 rounded-xl border border-gray-100 hover:shadow-lg transition-shadow duration-300">
                  <div className={cn(
                    'w-16 h-16 rounded-full flex items-center justify-center text-2xl mx-auto mb-4',
                    cert.color
                  )}>
                    {cert.icon}
                  </div>
                  <h4 className="font-semibold text-gray-900 mb-2">
                    {cert.name}
                  </h4>
                  <p className="text-gray-600 text-sm">
                    {cert.description}
                  </p>
                </div>
              </FadeInSection>
            ))}
          </div>
        </div>
      </FadeInSection>
    </div>
  );
};

export default SaasTrust;
