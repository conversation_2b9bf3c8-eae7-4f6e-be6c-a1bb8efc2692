import React from 'react';
import { cn } from '@/lib/utils';
import FadeInSection from './FadeInSection';

interface FunnelStage {
  id: number;
  title: string;
  description: string;
  icon: string;
  automation: string[];
  color: string;
  percentage: number;
}

const funnelStages: FunnelStage[] = [
  {
    id: 1,
    title: 'Lead Capture & Qualification',
    description: 'Capture and qualify leads from website and marketing channels',
    icon: '🎯',
    automation: [
      'Website form integration with Kommo CRM',
      'Lead scoring and automatic qualification',
      'Lead distribution to sales team members'
    ],
    color: 'from-cyan-500 to-cyan-600',
    percentage: 100
  },
  {
    id: 2,
    title: 'Trial/Demo Request Processing',
    description: 'Process trial requests and demo bookings efficiently',
    icon: '🚀',
    automation: [
      'Automated trial access provisioning notifications',
      'Demo booking calendar integration',
      'Follow-up sequences for trial users'
    ],
    color: 'from-blue-500 to-blue-600',
    percentage: 25
  },
  {
    id: 3,
    title: 'Sales Engagement & Nurturing',
    description: 'Engage prospects with personalized sales outreach',
    icon: '⚡',
    automation: [
      'Automated email sequences based on trial activity',
      'Task creation for sales team follow-ups',
      'Lead nurturing campaigns for different segments'
    ],
    color: 'from-indigo-500 to-indigo-600',
    percentage: 15
  },
  {
    id: 4,
    title: 'Opportunity Management',
    description: 'Track and manage sales opportunities effectively',
    icon: '🔍',
    automation: [
      'Deal stage progression tracking',
      'Automated reminders for follow-up activities',
      'Pipeline reporting and forecasting'
    ],
    color: 'from-purple-500 to-purple-600',
    percentage: 8
  },
  {
    id: 5,
    title: 'Proposal & Closing',
    description: 'Streamline proposal process and deal closing',
    icon: '💼',
    automation: [
      'Automated proposal generation and tracking',
      'Contract approval workflow automation',
      'Deal closing notifications and handoff'
    ],
    color: 'from-pink-500 to-pink-600',
    percentage: 5
  },
  {
    id: 6,
    title: 'Customer Onboarding & Retention',
    description: 'Manage customer lifecycle and expansion opportunities',
    icon: '📈',
    automation: [
      'Customer onboarding task automation',
      'Renewal reminders and upselling opportunities',
      'Customer satisfaction tracking and follow-up'
    ],
    color: 'from-rose-500 to-rose-600',
    percentage: 3
  }
];

const SaasFunnel: React.FC = () => {
  return (
    <div className="space-y-12">
      <FadeInSection>
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            SaaS & Tech Sales Process with Kommo CRM Automation
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            See how Kommo CRM streamlines your sales process from lead capture to customer retention,
            with automated workflows designed for SaaS and tech companies
          </p>
        </div>
      </FadeInSection>

      {/* Funnel Visualization */}
      <div className="relative">
        {/* Connection Lines */}
        <div className="hidden lg:block absolute left-1/2 top-0 bottom-0 w-0.5 bg-gradient-to-b from-cyan-200 via-purple-200 to-rose-200 transform -translate-x-1/2"></div>

        <div className="space-y-8">
          {funnelStages.map((stage, index) => (
            <FadeInSection key={stage.id} delay={index * 200}>
              <div className={cn(
                'relative flex flex-col lg:flex-row items-center gap-8',
                index % 2 === 0 ? 'lg:flex-row' : 'lg:flex-row-reverse'
              )}>
                {/* Stage Content */}
                <div className="flex-1 max-w-lg">
                  <div className="bg-white rounded-xl shadow-lg p-6 border border-gray-100 hover:shadow-xl transition-shadow duration-300">
                    <div className="flex items-center mb-4">
                      <div className={cn(
                        'w-12 h-12 rounded-full flex items-center justify-center text-2xl text-white mr-4',
                        `bg-gradient-to-r ${stage.color}`
                      )}>
                        {stage.icon}
                      </div>
                      <div>
                        <h3 className="text-xl font-bold text-gray-900">{stage.title}</h3>
                        <p className="text-gray-600 text-sm">{stage.description}</p>
                      </div>
                    </div>

                    <div className="space-y-3">
                      <h4 className="font-semibold text-gray-800 text-sm uppercase tracking-wide">
                        Automation Features:
                      </h4>
                      <ul className="space-y-2">
                        {stage.automation.map((feature, featureIndex) => (
                          <li key={featureIndex} className="flex items-start text-sm text-gray-700">
                            <span className="text-cyan-500 mr-2 mt-0.5">✓</span>
                            <span>{feature}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>

                {/* Stage Number & Percentage */}
                <div className="flex-shrink-0">
                  <div className="relative">
                    <div className={cn(
                      'w-20 h-20 rounded-full flex items-center justify-center text-white font-bold text-lg shadow-lg',
                      `bg-gradient-to-r ${stage.color}`
                    )}>
                      {stage.id}
                    </div>
                    <div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 text-center">
                      <div className="text-sm font-semibold text-gray-600">
                        {stage.percentage}%
                      </div>
                      <div className="text-xs text-gray-500">conversion</div>
                    </div>
                  </div>
                </div>

                {/* Funnel Bar */}
                <div className="flex-1 max-w-lg">
                  <div className="bg-gray-100 rounded-full h-8 overflow-hidden">
                    <div 
                      className={cn(
                        'h-full rounded-full transition-all duration-1000 ease-out',
                        `bg-gradient-to-r ${stage.color}`
                      )}
                      style={{ width: `${stage.percentage}%` }}
                    ></div>
                  </div>
                  <div className="mt-2 text-center">
                    <span className="text-sm font-medium text-gray-700">
                      {stage.percentage}% of visitors reach this stage
                    </span>
                  </div>
                </div>
              </div>
            </FadeInSection>
          ))}
        </div>
      </div>

      {/* SaaS Metrics */}
      <FadeInSection delay={1200}>
        <div className="bg-gradient-to-br from-cyan-50 to-blue-50 rounded-2xl p-8 mt-16">
          <div className="text-center mb-8">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              Key SaaS Metrics Optimized by CRM Automation
            </h3>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="w-16 h-16 bg-cyan-100 rounded-full flex items-center justify-center text-2xl text-cyan-600 mx-auto mb-4">
                📊
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">Product-Led Growth</h4>
              <p className="text-gray-600 text-sm">
                Track user activation, feature adoption, and product-qualified leads automatically
              </p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center text-2xl text-blue-600 mx-auto mb-4">
                🔄
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">Customer Lifecycle</h4>
              <p className="text-gray-600 text-sm">
                Manage the entire journey from trial to enterprise with automated touchpoints
              </p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-indigo-100 rounded-full flex items-center justify-center text-2xl text-indigo-600 mx-auto mb-4">
                📈
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">Revenue Expansion</h4>
              <p className="text-gray-600 text-sm">
                Identify upselling opportunities and prevent churn with predictive analytics
              </p>
            </div>
          </div>
        </div>
      </FadeInSection>


      {/* Success Metrics */}
      <FadeInSection delay={1600}>
        <div className="bg-gradient-to-r from-gray-900 to-cyan-900 rounded-2xl p-8 text-white">
          <div className="text-center mb-8">
            <h3 className="text-2xl font-bold mb-4">
              Typical SaaS Funnel Performance with CRM Automation
            </h3>
            <p className="text-cyan-100">
              Industry benchmarks for SaaS and tech company sales funnels
            </p>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            <div className="text-center">
              <div className="text-3xl font-bold text-cyan-400 mb-2">25%</div>
              <div className="text-sm text-cyan-200">Visitor to Trial</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-blue-400 mb-2">15%</div>
              <div className="text-sm text-cyan-200">Trial to Activation</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-purple-400 mb-2">8%</div>
              <div className="text-sm text-cyan-200">Activation to SQL</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-pink-400 mb-2">3%</div>
              <div className="text-sm text-cyan-200">Overall Conversion</div>
            </div>
          </div>
        </div>
      </FadeInSection>

      {/* Product-Led Growth Focus */}
      <FadeInSection delay={1800}>
        <div className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-2xl p-8">
          <div className="text-center mb-8">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              Product-Led Growth (PLG) Automation
            </h3>
            <p className="text-gray-600">
              Leverage product usage data to drive sales and customer success
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center p-6 bg-white rounded-xl shadow-sm">
              <div className="text-3xl mb-3">🎯</div>
              <h4 className="font-semibold text-gray-900 mb-2">Usage-Based Scoring</h4>
              <p className="text-gray-600 text-sm">
                Automatically score leads based on feature usage, API calls, and engagement depth
              </p>
            </div>
            
            <div className="text-center p-6 bg-white rounded-xl shadow-sm">
              <div className="text-3xl mb-3">⚡</div>
              <h4 className="font-semibold text-gray-900 mb-2">Activation Triggers</h4>
              <p className="text-gray-600 text-sm">
                Trigger sales outreach when users hit key activation milestones or usage thresholds
              </p>
            </div>
            
            <div className="text-center p-6 bg-white rounded-xl shadow-sm">
              <div className="text-3xl mb-3">📊</div>
              <h4 className="font-semibold text-gray-900 mb-2">Expansion Signals</h4>
              <p className="text-gray-600 text-sm">
                Identify upselling opportunities based on usage patterns and feature adoption
              </p>
            </div>
          </div>
        </div>
      </FadeInSection>
    </div>
  );
};

export default SaasFunnel;
