import React from 'react';
import { cn } from '@/lib/utils';
import FadeInSection from './FadeInSection';
import Image from 'next/image';

const partners = [
  { name: 'Kommo Certified Partner', logo: '/Crtifikat.webp', verified: true },
  { name: 'Calendly Integration Expert', logo: '📅', verified: true },
  { name: 'Zoom Integration Partner', logo: '🎥', verified: true },
  { name: 'Make.com Integration Expert', logo: '🔗', verified: true }
];

const achievements = [
  {
    metric: '320+',
    label: 'CRM Projects',
    description: 'Successfully implemented and optimized',
    icon: '🚀'
  },
  {
    metric: '10+',
    label: 'Years Experience',
    description: 'In digital marketing automation',
    icon: '⏰'
  },
  {
    metric: '2000+',
    label: 'Marketers Trained',
    description: 'On CRM and automation best practices',
    icon: '👥'
  },
  {
    metric: '95%',
    label: 'Client Satisfaction',
    description: 'Based on post-implementation surveys',
    icon: '⭐'
  }
];

const clientLogos = [
  { name: 'Digital Growth Agency', logo: '📈' },
  { name: 'Marketing Consultancy Pro', logo: '🎯' },
  { name: 'Creative Agency Masters', logo: '🎨' },
  { name: 'Brand Strategy Hub', logo: '✨' },
  { name: 'Business Development Co', logo: '💼' },
  { name: 'Growth Strategy Group', logo: '🚀' }
];

const certifications = [
  {
    name: 'Kommo CRM Expert',
    description: 'Advanced CRM implementation and optimization',
    icon: '🎯',
    color: 'bg-blue-100 text-blue-600'
  },
  {
    name: 'Calendly Integration Specialist',
    description: 'Expert-level scheduling and appointment automation',
    icon: '📅',
    color: 'bg-purple-100 text-purple-600'
  },
  {
    name: 'Zoom Integration Certified',
    description: 'Meeting automation and client communication expertise',
    icon: '🎥',
    color: 'bg-orange-100 text-orange-600'
  },
  {
    name: 'Make.com Partner',
    description: 'Advanced automation and integration solutions',
    icon: '🔗',
    color: 'bg-green-100 text-green-600'
  }
];

const DigitalMarketingTrust: React.FC = () => {
  return (
    <div className="space-y-16">
      {/* Certifications Section */}
      <FadeInSection>
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Certified Digital Marketing CRM Experts
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Trust your digital marketing CRM implementation to certified professionals with proven expertise
          </p>
        </div>
      </FadeInSection>

      {/* Main Certification Badge */}
      <FadeInSection delay={200}>
        <div className="text-center mb-12">
          <div className="inline-block bg-white rounded-lg p-6 shadow-xl max-w-3xl mx-auto">
            <img
              src="/Crtifikat.webp"
              alt="Kommo Partnership Certificate"
              className="w-full max-w-2xl mx-auto rounded-lg border border-gray-200"
            />
          </div>
        </div>
      </FadeInSection>


    </div>
  );
};

export default DigitalMarketingTrust;
