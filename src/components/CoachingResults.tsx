import React from 'react';
import { cn } from '@/lib/utils';
import FadeInSection from './FadeInSection';

interface ResultMetric {
  value: string;
  label: string;
  description: string;
  color: 'green' | 'blue' | 'purple' | 'orange';
  icon: string;
}

const results: ResultMetric[] = [
  {
    value: '+180%',
    label: 'Client Conversion Rate',
    description: 'Automated lead nurturing and follow-up',
    color: 'green',
    icon: '🎓'
  },
  {
    value: '92%',
    label: 'Client Satisfaction',
    description: 'Personalized communication and support',
    color: 'blue',
    icon: '⭐'
  },
  {
    value: '-60%',
    label: 'Administrative Time',
    description: 'Automated onboarding and client management',
    color: 'purple',
    icon: '⚡'
  },
  {
    value: '+150%',
    label: 'Revenue Growth',
    description: 'Better lead nurturing and upselling',
    color: 'orange',
    icon: '📈'
  }
];

const getColorClasses = (color: string) => {
  const colorMap = {
    green: {
      bg: 'bg-green-50',
      border: 'border-green-200',
      icon: 'bg-green-100 text-green-600',
      value: 'text-green-600'
    },
    blue: {
      bg: 'bg-blue-50',
      border: 'border-blue-200',
      icon: 'bg-blue-100 text-blue-600',
      value: 'text-blue-600'
    },
    purple: {
      bg: 'bg-purple-50',
      border: 'border-purple-200',
      icon: 'bg-purple-100 text-purple-600',
      value: 'text-purple-600'
    },
    orange: {
      bg: 'bg-orange-50',
      border: 'border-orange-200',
      icon: 'bg-orange-100 text-orange-600',
      value: 'text-orange-600'
    }
  };
  return colorMap[color as keyof typeof colorMap] || colorMap.blue;
};

const CoachingResults: React.FC = () => {
  return (
    <div className="space-y-16">
      {/* Results Metrics */}
      <FadeInSection>
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Measurable Results from Coaching CRM Implementation
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            See the tangible benefits our clients achieve with Kommo CRM for coaching and online education
          </p>
        </div>
      </FadeInSection>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {results.map((result, index) => {
          const colors = getColorClasses(result.color);
          return (
            <FadeInSection key={index} delay={index * 150}>
              <div className={cn(
                'p-6 rounded-xl border-2 transition-all duration-300 hover:shadow-lg hover:-translate-y-1',
                colors.bg,
                colors.border
              )}>
                <div className={cn(
                  'w-12 h-12 rounded-full flex items-center justify-center text-2xl mb-4',
                  colors.icon
                )}>
                  {result.icon}
                </div>
                <div className={cn('text-3xl font-bold mb-2', colors.value)}>
                  {result.value}
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  {result.label}
                </h3>
                <p className="text-gray-600 text-sm">
                  {result.description}
                </p>
              </div>
            </FadeInSection>
          );
        })}
      </div>

      {/* ROI Calculator Preview */}
      <FadeInSection delay={1600}>
        <div className="bg-white rounded-2xl p-8 border border-gray-100 shadow-lg">
          <div className="text-center mb-8">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              Calculate Your Potential ROI
            </h3>
            <p className="text-gray-600 max-w-2xl mx-auto">
              See how much time and money you could save with automated coaching workflows
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center p-6 bg-gray-50 rounded-xl">
              <div className="text-2xl font-bold text-gray-900 mb-2">20 hrs/week</div>
              <div className="text-sm text-gray-600 mb-4">Time saved on admin tasks</div>
              <div className="text-lg font-semibold text-green-600">= $2,000/month</div>
              <div className="text-xs text-gray-500">at $25/hour value</div>
            </div>
            
            <div className="text-center p-6 bg-gray-50 rounded-xl">
              <div className="text-2xl font-bold text-gray-900 mb-2">3x more</div>
              <div className="text-sm text-gray-600 mb-4">Clients served efficiently</div>
              <div className="text-lg font-semibold text-green-600">= $15,000/month</div>
              <div className="text-xs text-gray-500">additional revenue</div>
            </div>
            
            <div className="text-center p-6 bg-gray-50 rounded-xl">
              <div className="text-2xl font-bold text-gray-900 mb-2">85%</div>
              <div className="text-sm text-gray-600 mb-4">Higher client satisfaction</div>
              <div className="text-lg font-semibold text-green-600">= Better reviews</div>
              <div className="text-xs text-gray-500">and referrals</div>
            </div>
          </div>

          <div className="text-center mt-8">
            <div className="inline-block px-6 py-3 bg-green-600 text-white rounded-lg font-semibold hover:bg-green-700 transition-colors cursor-pointer">
              Calculate Your Specific ROI
            </div>
          </div>
        </div>
      </FadeInSection>
    </div>
  );
};

export default CoachingResults;
