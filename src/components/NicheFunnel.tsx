import React from 'react';
import { cn } from '@/lib/utils';
import FadeInSection from './FadeInSection';

interface FunnelStage {
  id: number;
  title: string;
  description: string;
  icon: string;
  automation: string[];
  color: string;
  percentage: number;
}

const funnelStages: FunnelStage[] = [
  {
    id: 1,
    title: 'Niche Audience Discovery',
    description: 'Attract specialized customers and enthusiasts',
    icon: '🎯',
    automation: [
      'Targeted content marketing for niche interests',
      'Community engagement and influencer partnerships',
      'SEO optimization for specialized keywords'
    ],
    color: 'from-emerald-500 to-emerald-600',
    percentage: 100
  },
  {
    id: 2,
    title: 'Interest & Education',
    description: 'Educate prospects about specialized products',
    icon: '🎓',
    automation: [
      'Educational email sequences and product guides',
      'Automated webinar invitations and follow-ups',
      'Personalized content based on interests'
    ],
    color: 'from-teal-500 to-teal-600',
    percentage: 60
  },
  {
    id: 3,
    title: 'Trust Building & Social Proof',
    description: 'Build credibility in the niche community',
    icon: '🤝',
    automation: [
      'Customer testimonial and review campaigns',
      'Expert endorsement and certification displays',
      'Community feedback and social proof automation'
    ],
    color: 'from-cyan-500 to-cyan-600',
    percentage: 40
  },
  {
    id: 4,
    title: 'Consideration & Comparison',
    description: 'Help customers evaluate specialized options',
    icon: '🔍',
    automation: [
      'Product comparison guides and tools',
      'Personalized recommendation engines',
      'Expert consultation scheduling'
    ],
    color: 'from-blue-500 to-blue-600',
    percentage: 25
  },
  {
    id: 5,
    title: 'Purchase Decision',
    description: 'Convert educated prospects to customers',
    icon: '💳',
    automation: [
      'Limited-time offers for niche products',
      'Abandoned cart recovery with education',
      'Payment plan options for premium items'
    ],
    color: 'from-indigo-500 to-indigo-600',
    percentage: 15
  },
  {
    id: 6,
    title: 'Community & Loyalty',
    description: 'Build long-term niche community relationships',
    icon: '👥',
    automation: [
      'VIP customer community access',
      'Exclusive product launches and previews',
      'Referral programs within niche communities'
    ],
    color: 'from-purple-500 to-purple-600',
    percentage: 12
  }
];

const NicheFunnel: React.FC = () => {
  return (
    <div className="space-y-12">
      <FadeInSection>
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Niche eCommerce Customer Journey with Kommo CRM
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            See how Kommo CRM automates specialized customer journeys for niche markets,
            from discovery to community building and long-term loyalty
          </p>
        </div>
      </FadeInSection>

      {/* Funnel Visualization */}
      <div className="relative">
        {/* Connection Lines */}
        <div className="hidden lg:block absolute left-1/2 top-0 bottom-0 w-0.5 bg-gradient-to-b from-emerald-200 via-cyan-200 to-purple-200 transform -translate-x-1/2"></div>

        <div className="space-y-8">
          {funnelStages.map((stage, index) => (
            <FadeInSection key={stage.id} delay={index * 200}>
              <div className={cn(
                'relative flex flex-col lg:flex-row items-center gap-8',
                index % 2 === 0 ? 'lg:flex-row' : 'lg:flex-row-reverse'
              )}>
                {/* Stage Content */}
                <div className="flex-1 max-w-lg">
                  <div className="bg-white rounded-xl shadow-lg p-6 border border-gray-100 hover:shadow-xl transition-shadow duration-300">
                    <div className="flex items-center mb-4">
                      <div className={cn(
                        'w-12 h-12 rounded-full flex items-center justify-center text-2xl text-white mr-4',
                        `bg-gradient-to-r ${stage.color}`
                      )}>
                        {stage.icon}
                      </div>
                      <div>
                        <h3 className="text-xl font-bold text-gray-900">{stage.title}</h3>
                        <p className="text-gray-600 text-sm">{stage.description}</p>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <h4 className="font-semibold text-gray-800 text-sm">CRM Automation:</h4>
                      <ul className="space-y-1">
                        {stage.automation.map((item, idx) => (
                          <li key={idx} className="text-gray-600 text-sm flex items-start">
                            <span className="text-emerald-500 mr-2">•</span>
                            {item}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>

                {/* Conversion Rate Display */}
                <div className="flex-shrink-0">
                  <div className="relative">
                    {/* Circular Progress */}
                    <div className="w-32 h-32 relative">
                      <svg className="w-32 h-32 transform -rotate-90" viewBox="0 0 120 120">
                        {/* Background circle */}
                        <circle
                          cx="60"
                          cy="60"
                          r="50"
                          fill="none"
                          stroke="#e5e7eb"
                          strokeWidth="8"
                        />
                        {/* Progress circle */}
                        <circle
                          cx="60"
                          cy="60"
                          r="50"
                          fill="none"
                          stroke="url(#emeraldGradient)"
                          strokeWidth="8"
                          strokeLinecap="round"
                          strokeDasharray={`${2 * Math.PI * 50}`}
                          strokeDashoffset={`${2 * Math.PI * 50 * (1 - stage.percentage / 100)}`}
                          className="transition-all duration-1000"
                        />
                        {/* Gradient definition */}
                        <defs>
                          <linearGradient id="emeraldGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" stopColor="#10b981" />
                            <stop offset="100%" stopColor="#14b8a6" />
                          </linearGradient>
                        </defs>
                      </svg>

                      {/* Center content */}
                      <div className="absolute inset-0 flex flex-col items-center justify-center">
                        <div className="text-2xl font-bold text-emerald-600">{stage.percentage}%</div>
                        <div className="text-xs text-gray-500 text-center">Conversion</div>
                      </div>
                    </div>

                    {/* Stage number badge */}
                    <div className="absolute -top-2 -right-2 w-8 h-8 bg-emerald-500 text-white rounded-full flex items-center justify-center text-sm font-bold shadow-lg">
                      {stage.id}
                    </div>
                  </div>
                </div>
              </div>
            </FadeInSection>
          ))}
        </div>
      </div>

      {/* Summary Results */}
      <FadeInSection delay={1200}>
        <div className="text-center bg-gradient-to-r from-emerald-50 to-teal-50 rounded-2xl p-8">
          <h3 className="text-2xl font-bold text-gray-900 mb-6">
            Complete Automation Results
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="text-3xl font-bold text-emerald-600 mb-2">150%</div>
              <div className="text-lg font-semibold text-gray-900">Higher Conversion Rates</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-emerald-600 mb-2">80%</div>
              <div className="text-lg font-semibold text-gray-900">Better Customer Retention</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-emerald-600 mb-2">3x</div>
              <div className="text-lg font-semibold text-gray-900">Faster Customer Journey</div>
            </div>
          </div>
        </div>
      </FadeInSection>
    </div>
  );
};

export default NicheFunnel;
