import React from 'react';
import { cn } from '@/lib/utils';
import FadeInSection from './FadeInSection';

interface FunnelStage {
  id: number;
  title: string;
  description: string;
  icon: string;
  automation: string[];
  color: string;
  percentage: number;
}

const funnelStages: FunnelStage[] = [
  {
    id: 1,
    title: 'Lead Generation',
    description: 'Attract potential clients through various digital channels',
    icon: '🎯',
    automation: [
      'Multi-channel lead capture (website, forms, referrals)',
      'Automatic lead scoring based on engagement',
      'Instant notification to account managers'
    ],
    color: 'from-blue-500 to-blue-600',
    percentage: 100
  },
  {
    id: 2,
    title: 'Qualification',
    description: 'Assess client needs, budget, and marketing goals',
    icon: '🔍',
    automation: [
      'Automated qualification questionnaire',
      'Budget and goals assessment forms',
      'Industry-specific requirement analysis'
    ],
    color: 'from-indigo-500 to-indigo-600',
    percentage: 70
  },
  {
    id: 3,
    title: 'Proposal & Strategy',
    description: 'Create customized marketing strategy and proposal',
    icon: '📋',
    automation: [
      'Template-based proposal generation',
      'Automated competitor analysis reports',
      'Custom strategy document creation'
    ],
    color: 'from-purple-500 to-purple-600',
    percentage: 45
  },
  {
    id: 4,
    title: 'Negotiation',
    description: 'Discuss terms, pricing, and service scope',
    icon: '🤝',
    automation: [
      'Automated follow-up sequences',
      'Contract template generation',
      'Pricing calculator integration'
    ],
    color: 'from-pink-500 to-pink-600',
    percentage: 30
  },
  {
    id: 5,
    title: 'Onboarding',
    description: 'Client setup and service launch preparation',
    icon: '🚀',
    automation: [
      'Automated onboarding checklist',
      'Account setup workflows',
      'Team assignment and notifications'
    ],
    color: 'from-orange-500 to-orange-600',
    percentage: 25
  },
  {
    id: 6,
    title: 'Client Management',
    description: 'Ongoing client relationship and service delivery',
    icon: '📊',
    automation: [
      'Automated client reports',
      'Service delivery alerts',
      'Client communication scheduling'
    ],
    color: 'from-green-500 to-green-600',
    percentage: 20
  }
];

const DigitalMarketingFunnel: React.FC = () => {
  return (
    <div className="space-y-12">
      <FadeInSection>
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Digital Marketing Sales Funnel with Automation
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            See how Kommo CRM automates every stage of your digital marketing agency workflow, 
            from lead generation to ongoing client management
          </p>
        </div>
      </FadeInSection>

      {/* Funnel Visualization */}
      <div className="relative">
        {/* Connection Lines */}
        <div className="hidden lg:block absolute left-1/2 top-0 bottom-0 w-0.5 bg-gradient-to-b from-blue-200 via-purple-200 to-green-200 transform -translate-x-1/2"></div>

        <div className="space-y-8">
          {funnelStages.map((stage, index) => (
            <FadeInSection key={stage.id} delay={index * 200}>
              <div className={cn(
                'relative flex flex-col lg:flex-row items-center gap-8',
                index % 2 === 0 ? 'lg:flex-row' : 'lg:flex-row-reverse'
              )}>
                {/* Stage Content */}
                <div className="flex-1 max-w-lg">
                  <div className="bg-white rounded-xl shadow-lg p-6 border border-gray-100 hover:shadow-xl transition-shadow duration-300">
                    <div className="flex items-center mb-4">
                      <div className={cn(
                        'w-12 h-12 rounded-full flex items-center justify-center text-2xl text-white mr-4',
                        `bg-gradient-to-r ${stage.color}`
                      )}>
                        {stage.icon}
                      </div>
                      <div>
                        <h3 className="text-xl font-bold text-gray-900">{stage.title}</h3>
                        <p className="text-gray-600 text-sm">{stage.description}</p>
                      </div>
                    </div>

                    <div className="space-y-3">
                      <h4 className="font-semibold text-gray-800 text-sm uppercase tracking-wide">
                        Automation Features:
                      </h4>
                      <ul className="space-y-2">
                        {stage.automation.map((feature, featureIndex) => (
                          <li key={featureIndex} className="flex items-start text-sm text-gray-700">
                            <span className="text-green-500 mr-2 mt-0.5">✓</span>
                            <span>{feature}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>

                {/* Stage Number & Percentage */}
                <div className="flex-shrink-0">
                  <div className="relative">
                    <div className={cn(
                      'w-20 h-20 rounded-full flex items-center justify-center text-white font-bold text-lg shadow-lg',
                      `bg-gradient-to-r ${stage.color}`
                    )}>
                      {stage.id}
                    </div>
                    <div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 text-center">
                      <div className="text-sm font-semibold text-gray-600">
                        {stage.percentage}%
                      </div>
                      <div className="text-xs text-gray-500">conversion</div>
                    </div>
                  </div>
                </div>

                {/* Funnel Bar */}
                <div className="flex-1 max-w-lg">
                  <div className="bg-gray-100 rounded-full h-8 overflow-hidden">
                    <div 
                      className={cn(
                        'h-full rounded-full transition-all duration-1000 ease-out',
                        `bg-gradient-to-r ${stage.color}`
                      )}
                      style={{ width: `${stage.percentage}%` }}
                    ></div>
                  </div>
                  <div className="mt-2 text-center">
                    <span className="text-sm font-medium text-gray-700">
                      {stage.percentage}% of leads reach this stage
                    </span>
                  </div>
                </div>
              </div>
            </FadeInSection>
          ))}
        </div>
      </div>

      {/* Key Benefits */}
      <FadeInSection delay={1200}>
        <div className="bg-gradient-to-br from-blue-50 to-purple-50 rounded-2xl p-8 mt-16">
          <div className="text-center mb-8">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              Why This Funnel Works for Digital Marketing Agencies
            </h3>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center text-2xl text-blue-600 mx-auto mb-4">
                🎯
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">Multi-Channel Focus</h4>
              <p className="text-gray-600 text-sm">
                Designed for agencies managing multiple client relationships across various service channels
              </p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center text-2xl text-purple-600 mx-auto mb-4">
                📊
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">Sales Tracking</h4>
              <p className="text-gray-600 text-sm">
                Built-in CRM analytics and reporting for both agency sales and client relationships
              </p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center text-2xl text-green-600 mx-auto mb-4">
                ⚡
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">Scalable Automation</h4>
              <p className="text-gray-600 text-sm">
                Automated workflows that scale with your agency growth and client portfolio
              </p>
            </div>
          </div>
        </div>
      </FadeInSection>
    </div>
  );
};

export default DigitalMarketingFunnel;
