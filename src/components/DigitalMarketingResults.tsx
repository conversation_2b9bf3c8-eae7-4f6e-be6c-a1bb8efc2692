import React from 'react';
import { cn } from '@/lib/utils';
import FadeInSection from './FadeInSection';

interface ResultMetric {
  value: string;
  label: string;
  description: string;
  color: 'green' | 'blue' | 'purple' | 'orange';
  icon: string;
}

const results: ResultMetric[] = [
  {
    value: '+60%',
    label: 'Client Retention Rate',
    description: 'Better communication and client management',
    color: 'green',
    icon: '🤝'
  },
  {
    value: '90%',
    label: 'Team Productivity',
    description: 'Automated workflows and task management',
    color: 'blue',
    icon: '⚡'
  },
  {
    value: '-40%',
    label: 'Administrative Time',
    description: 'Automated reporting and client updates',
    color: 'purple',
    icon: '📊'
  },
  {
    value: '+35%',
    label: 'Revenue Growth',
    description: 'Better lead management and upselling',
    color: 'orange',
    icon: '📈'
  }
];

const testimonials = [
  {
    quote: "<PERSON><PERSON><PERSON> transformed how we manage our 50+ clients. Automated reporting alone saves us 20 hours per week, and our client satisfaction scores have never been higher.",
    author: "<PERSON>",
    position: "Agency Director",
    company: "Digital Growth Partners",
    avatar: "👩‍💼"
  },
  {
    quote: "The client management and communication features are game-changers. We can now handle 3x more clients with the same team size.",
    author: "<PERSON>",
    position: "Operations Manager",
    company: "Performance Marketing Co.",
    avatar: "👨‍💼"
  },
  {
    quote: "Integration with our business tools and CRM created a unified dashboard that our entire team loves. No more switching between 10 different tools.",
    author: "Emily Chen",
    position: "Digital Strategy Lead",
    company: "Creative Digital Agency",
    avatar: "👩‍💻"
  }
];

const getColorClasses = (color: string) => {
  const colorMap = {
    green: {
      bg: 'bg-green-50',
      border: 'border-green-200',
      icon: 'bg-green-100 text-green-600',
      value: 'text-green-600'
    },
    blue: {
      bg: 'bg-blue-50',
      border: 'border-blue-200',
      icon: 'bg-blue-100 text-blue-600',
      value: 'text-blue-600'
    },
    purple: {
      bg: 'bg-purple-50',
      border: 'border-purple-200',
      icon: 'bg-purple-100 text-purple-600',
      value: 'text-purple-600'
    },
    orange: {
      bg: 'bg-orange-50',
      border: 'border-orange-200',
      icon: 'bg-orange-100 text-orange-600',
      value: 'text-orange-600'
    }
  };
  return colorMap[color as keyof typeof colorMap] || colorMap.blue;
};

const DigitalMarketingResults: React.FC = () => {
  return (
    <div className="space-y-16">
      {/* Results Metrics */}
      <FadeInSection>
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Measurable Results from Digital Marketing CRM Implementation
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            See the tangible benefits our clients achieve with Kommo CRM for digital marketing agencies
          </p>
        </div>
      </FadeInSection>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {results.map((result, index) => {
          const colors = getColorClasses(result.color);
          return (
            <FadeInSection key={index} delay={index * 150}>
              <div className={cn(
                'p-6 rounded-xl border-2 transition-all duration-300 hover:shadow-lg hover:-translate-y-1',
                colors.bg,
                colors.border
              )}>
                <div className={cn(
                  'w-12 h-12 rounded-full flex items-center justify-center text-2xl mb-4',
                  colors.icon
                )}>
                  {result.icon}
                </div>
                <div className={cn('text-3xl font-bold mb-2', colors.value)}>
                  {result.value}
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  {result.label}
                </h3>
                <p className="text-gray-600 text-sm">
                  {result.description}
                </p>
              </div>
            </FadeInSection>
          );
        })}
      </div>

    </div>
  );
};

export default DigitalMarketingResults;
