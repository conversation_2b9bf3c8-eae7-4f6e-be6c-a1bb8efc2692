import React from 'react';
import { cn } from '@/lib/utils';
import FadeInSection from './FadeInSection';

interface ResultMetric {
  value: string;
  label: string;
  description: string;
  color: string;
  icon: string;
}

const results: ResultMetric[] = [
  {
    value: '+150%',
    label: 'Conversion Rate',
    description: 'Higher conversions through targeted messaging',
    color: 'emerald',
    icon: '🎯'
  },
  {
    value: '80%',
    label: 'Customer Retention',
    description: 'Better loyalty in niche communities',
    color: 'teal',
    icon: '🤝'
  },
  {
    value: '-60%',
    label: 'Customer Acquisition Cost',
    description: 'More efficient targeting and referrals',
    color: 'cyan',
    icon: '💰'
  },
  {
    value: '+200%',
    label: 'Customer Lifetime Value',
    description: 'Stronger relationships and repeat purchases',
    color: 'blue',
    icon: '📈'
  }
];

const getColorClasses = (color: string) => {
  const colorMap = {
    emerald: {
      bg: 'bg-emerald-50 border-emerald-200',
      text: 'text-emerald-600',
      icon: 'bg-emerald-100 text-emerald-600'
    },
    teal: {
      bg: 'bg-teal-50 border-teal-200',
      text: 'text-teal-600',
      icon: 'bg-teal-100 text-teal-600'
    },
    cyan: {
      bg: 'bg-cyan-50 border-cyan-200',
      text: 'text-cyan-600',
      icon: 'bg-cyan-100 text-cyan-600'
    },
    blue: {
      bg: 'bg-blue-50 border-blue-200',
      text: 'text-blue-600',
      icon: 'bg-blue-100 text-blue-600'
    }
  };
  return colorMap[color as keyof typeof colorMap] || colorMap.emerald;
};

const NicheResults: React.FC = () => {
  return (
    <div className="space-y-16">
      {/* Results Metrics */}
      <FadeInSection>
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            CRM Implementation Results for Niche eCommerce
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            See the measurable impact our Kommo CRM implementation delivers for niche eCommerce businesses and specialized retailers
          </p>
        </div>
      </FadeInSection>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {results.map((result, index) => {
          const colors = getColorClasses(result.color);
          return (
            <FadeInSection key={index} delay={index * 150}>
              <div className={cn(
                'p-6 rounded-xl border-2 transition-all duration-300 hover:shadow-lg hover:-translate-y-1',
                colors.bg,
                colors.text
              )}>
                <div className={cn(
                  'w-12 h-12 rounded-full flex items-center justify-center text-2xl mb-4',
                  colors.icon
                )}>
                  {result.icon}
                </div>
                <div className="text-3xl font-bold mb-2">{result.value}</div>
                <div className="text-lg font-semibold text-gray-900 mb-1">{result.label}</div>
                <div className="text-sm text-gray-600">{result.description}</div>
              </div>
            </FadeInSection>
          );
        })}
      </div>


      {/* Call to Action */}
      <FadeInSection delay={1400}>
        <div className="text-center bg-gradient-to-r from-emerald-50 to-teal-50 rounded-2xl p-8">
          <h3 className="text-2xl font-bold text-gray-900 mb-4">
            Ready to See Similar Results for Your Niche Business?
          </h3>
          <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
            Join successful niche retailers who've transformed their customer relationships and increased sales with our specialized CRM implementation.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button className="px-8 py-3 bg-emerald-600 text-white rounded-lg font-semibold hover:bg-emerald-700 transition-colors duration-300 shadow-lg hover:shadow-xl">
              Get Free Consultation
            </button>
            <button className="px-8 py-3 border-2 border-emerald-600 text-emerald-600 rounded-lg font-semibold hover:bg-emerald-600 hover:text-white transition-all duration-300">
              View Case Studies
            </button>
          </div>
        </div>
      </FadeInSection>
    </div>
  );
};

export default NicheResults;
