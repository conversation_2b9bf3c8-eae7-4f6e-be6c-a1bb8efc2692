'use client';

import React from 'react';
import FadeInSection from './FadeInSection';
import { cn } from '@/lib/utils';

interface FunnelStage {
  id: number;
  title: string;
  description: string;
  icon: string;
  automation: string[];
  color: string;
  percentage: number;
}

const funnelStages: FunnelStage[] = [
  {
    id: 1,
    title: 'Lead Generation',
    description: 'Capture candidate and client inquiries',
    icon: '🎯',
    automation: [
      'Multi-channel lead capture (website, forms, referrals)',
      'Automatic lead qualification and scoring',
      'Instant notification to recruiters'
    ],
    color: 'from-blue-500 to-blue-600',
    percentage: 100
  },
  {
    id: 2,
    title: 'Initial Screening',
    description: 'Qualify candidates and understand client needs',
    icon: '📋',
    automation: [
      'Automated screening questionnaires',
      'Skill assessment scheduling',
      'Client requirement documentation'
    ],
    color: 'from-indigo-500 to-indigo-600',
    percentage: 75
  },
  {
    id: 3,
    title: 'Candidate Matching',
    description: 'Match qualified candidates with client requirements',
    icon: '🎯',
    automation: [
      'Automated requirement analysis',
      'Smart candidate-job matching',
      'Personalized candidate profiles via email'
    ],
    color: 'from-purple-500 to-purple-600',
    percentage: 60
  },
  {
    id: 4,
    title: 'Interview Coordination',
    description: 'Schedule and manage interview processes',
    icon: '🤝',
    automation: [
      'Online booking system for interviews',
      'Automatic calendar synchronization',
      'Reminder notifications for all parties'
    ],
    color: 'from-pink-500 to-pink-600',
    percentage: 45
  },
  {
    id: 5,
    title: 'Offer Management',
    description: 'Handle negotiations and offer processes',
    icon: '📄',
    automation: [
      'Automated offer document generation',
      'Negotiation timeline tracking',
      'Contract management workflows'
    ],
    color: 'from-red-500 to-red-600',
    percentage: 30
  },
  {
    id: 6,
    title: 'Placement & Follow-up',
    description: 'Complete placements and maintain relationships',
    icon: '✅',
    automation: [
      'Onboarding checklist automation',
      'Post-placement satisfaction surveys',
      'Long-term relationship nurturing'
    ],
    color: 'from-green-500 to-green-600',
    percentage: 20
  }
];

export default function RecruitingFunnel() {
  return (
    <section className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <FadeInSection className="text-center mb-16">
          <div className="inline-block px-4 py-2 mb-6 bg-blue-100 text-blue-600 rounded-full text-sm font-semibold">
            🔄 Automated Workflow
          </div>
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Complete Recruiting Process Automation
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            From initial candidate contact to successful placement - every step optimized with Kommo CRM
          </p>
        </FadeInSection>

        <div className="space-y-16">
          {funnelStages.map((stage, index) => (
            <FadeInSection key={stage.id} delay={index * 200}>
              <div className={cn(
                'relative flex flex-col lg:flex-row items-center gap-8',
                index % 2 === 0 ? 'lg:flex-row' : 'lg:flex-row-reverse'
              )}>
                {/* Stage Content */}
                <div className="flex-1 max-w-lg">
                  <div className="bg-white rounded-xl shadow-lg p-6 border border-gray-100 hover:shadow-xl transition-shadow duration-300">
                    <div className="flex items-center mb-4">
                      <div className={cn(
                        'w-12 h-12 rounded-full flex items-center justify-center text-2xl text-white mr-4',
                        `bg-gradient-to-r ${stage.color}`
                      )}>
                        {stage.icon}
                      </div>
                      <div>
                        <h3 className="text-xl font-semibold text-gray-900">{stage.title}</h3>
                        <p className="text-gray-600 text-sm">{stage.description}</p>
                      </div>
                    </div>
                    
                    <div className="space-y-3">
                      <h4 className="font-semibold text-gray-900 text-sm">Automation Features:</h4>
                      {stage.automation.map((feature, idx) => (
                        <div key={idx} className="flex items-start">
                          <span className="text-green-500 mr-2 mt-1">✓</span>
                          <span className="text-gray-700 text-sm">{feature}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>

                {/* Progress Visualization */}
                <div className="flex-1 max-w-lg">
                  <div className="bg-gray-50 rounded-xl p-6">
                    <div className="flex items-center justify-between mb-4">
                      <span className="text-gray-700 font-medium">Conversion Rate</span>
                      <span className="text-2xl font-bold text-gray-900">{stage.percentage}%</span>
                    </div>
                    
                    <div className="w-full bg-gray-200 rounded-full h-3 mb-4">
                      <div 
                        className={cn(
                          'h-3 rounded-full transition-all duration-1000',
                          `bg-gradient-to-r ${stage.color}`
                        )}
                        style={{ width: `${stage.percentage}%` }}
                      ></div>
                    </div>
                    
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div className="text-center">
                        <div className="text-2xl font-bold text-gray-900">
                          {stage.id === 1 ? '100' : Math.floor(100 * (stage.percentage / 100))}
                        </div>
                        <div className="text-gray-600">Candidates</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-green-600">
                          {stage.id === 6 ? '20' : Math.floor(stage.percentage * 0.8)}
                        </div>
                        <div className="text-gray-600">Qualified</div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Connecting Arrow */}
                {index < funnelStages.length - 1 && (
                  <div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 lg:hidden">
                    <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white">
                      ↓
                    </div>
                  </div>
                )}
              </div>
            </FadeInSection>
          ))}
        </div>

        {/* Summary Stats */}
        <FadeInSection delay={1200} className="mt-20">
          <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl p-8 text-white text-center">
            <h3 className="text-2xl font-bold mb-6">Complete Automation Results</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div>
                <div className="text-3xl font-bold mb-2">45%</div>
                <div className="text-blue-100">More Successful Placements</div>
              </div>
              <div>
                <div className="text-3xl font-bold mb-2">60%</div>
                <div className="text-blue-100">Less Manual Work</div>
              </div>
              <div>
                <div className="text-3xl font-bold mb-2">3x</div>
                <div className="text-blue-100">Faster Time-to-Fill</div>
              </div>
            </div>
          </div>
        </FadeInSection>
      </div>
    </section>
  );
}
