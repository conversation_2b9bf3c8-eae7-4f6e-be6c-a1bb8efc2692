import React from 'react';
import { cn } from '@/lib/utils';
import FadeInSection from './FadeInSection';

interface ResultMetric {
  value: string;
  label: string;
  description: string;
  color: 'green' | 'blue' | 'purple' | 'orange';
  icon: string;
}

const results: ResultMetric[] = [
  {
    value: '+40%',
    label: 'Project Efficiency',
    description: 'Streamlined workflows and better project tracking',
    color: 'green',
    icon: '🏗️'
  },
  {
    value: '95%',
    label: 'CRM Adoption Rate',
    description: 'Project managers actively use the system daily',
    color: 'blue',
    icon: '📱'
  },
  {
    value: '-55%',
    label: 'Less Admin Tasks',
    description: 'Automated documentation and reporting',
    color: 'purple',
    icon: '⚡'
  },
  {
    value: '+30%',
    label: 'Client Satisfaction',
    description: 'Better communication and project transparency',
    color: 'orange',
    icon: '📈'
  }
];

const testimonials = [
  {
    quote: "Kommo CRM transformed our project management. We're completing 40% more projects on time and our clients love the transparency.",
    author: "<PERSON>",
    role: "Project Manager",
    company: "BuildRight Construction"
  },
  {
    quote: "The automated client communication and progress tracking features have revolutionized how we work. Our team is more productive and clients are happier.",
    author: "<PERSON>",
    role: "Operations Director", 
    company: "Premier Contractors LLC"
  },
  {
    quote: "Integration with our accounting system and automatic progress reports saved us 20+ hours per week. ROI was achieved in just 8 weeks.",
    author: "James Wilson",
    role: "Company Owner",
    company: "Wilson Construction Group"
  }
];

function getColorClasses(color: string) {
  const colorMap = {
    green: {
      bg: 'bg-green-50',
      border: 'border-green-200',
      icon: 'bg-green-100 text-green-600',
      value: 'text-green-600'
    },
    blue: {
      bg: 'bg-blue-50',
      border: 'border-blue-200', 
      icon: 'bg-blue-100 text-blue-600',
      value: 'text-blue-600'
    },
    purple: {
      bg: 'bg-purple-50',
      border: 'border-purple-200',
      icon: 'bg-purple-100 text-purple-600', 
      value: 'text-purple-600'
    },
    orange: {
      bg: 'bg-orange-50',
      border: 'border-orange-200',
      icon: 'bg-orange-100 text-orange-600',
      value: 'text-orange-600'
    }
  };
  return colorMap[color as keyof typeof colorMap];
}

export default function ConstructionResults() {
  return (
    <section className="py-20 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <FadeInSection className="text-center mb-16">
          <div className="inline-block px-4 py-2 mb-6 bg-orange-100 text-orange-600 rounded-full text-sm font-semibold">
            📈 Proven Results
          </div>
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Real Results from Construction Companies
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            See how Kommo CRM has transformed construction processes for companies like yours
          </p>
        </FadeInSection>

        {/* Results Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
          {results.map((result, index) => {
            const colors = getColorClasses(result.color);
            return (
              <FadeInSection key={index} delay={index * 150}>
                <div className={cn(
                  'p-6 rounded-xl border-2 transition-all duration-300 hover:shadow-lg hover:-translate-y-1',
                  colors.bg,
                  colors.border
                )}>
                  <div className={cn(
                    'w-12 h-12 rounded-full flex items-center justify-center text-2xl mb-4',
                    colors.icon
                  )}>
                    {result.icon}
                  </div>
                  <div className={cn('text-3xl font-bold mb-2', colors.value)}>
                    {result.value}
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    {result.label}
                  </h3>
                  <p className="text-gray-600 text-sm">
                    {result.description}
                  </p>
                </div>
              </FadeInSection>
            );
          })}
        </div>


        {/* Before vs After */}
        <FadeInSection delay={1200}>
          <h3 className="text-2xl font-bold text-gray-900 text-center mb-12">
            Before vs After Kommo CRM
          </h3>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-16">
            <div className="bg-red-50 rounded-xl p-8 border-2 border-red-200">
              <h4 className="text-xl font-bold text-red-700 mb-6 flex items-center">
                <span className="mr-3">❌</span>
                Before: Manual Construction Management
              </h4>
              <ul className="space-y-3">
                <li className="flex items-start">
                  <span className="text-red-500 mr-3 mt-1">✗</span>
                  <span className="text-gray-700">Spreadsheet-based project tracking</span>
                </li>
                <li className="flex items-start">
                  <span className="text-red-500 mr-3 mt-1">✗</span>
                  <span className="text-gray-700">Manual scheduling and coordination</span>
                </li>
                <li className="flex items-start">
                  <span className="text-red-500 mr-3 mt-1">✗</span>
                  <span className="text-gray-700">Inconsistent client communication</span>
                </li>
                <li className="flex items-start">
                  <span className="text-red-500 mr-3 mt-1">✗</span>
                  <span className="text-gray-700">No project profitability insights</span>
                </li>
                <li className="flex items-start">
                  <span className="text-red-500 mr-3 mt-1">✗</span>
                  <span className="text-gray-700">Lost projects due to poor follow-up</span>
                </li>
              </ul>
            </div>

            <div className="bg-green-50 rounded-xl p-8 border-2 border-green-200">
              <h4 className="text-xl font-bold text-green-700 mb-6 flex items-center">
                <span className="mr-3">✅</span>
                After: Automated CRM System
              </h4>
              <ul className="space-y-3">
                <li className="flex items-start">
                  <span className="text-green-500 mr-3 mt-1">✓</span>
                  <span className="text-gray-700">Centralized project and client database</span>
                </li>
                <li className="flex items-start">
                  <span className="text-green-500 mr-3 mt-1">✓</span>
                  <span className="text-gray-700">Automated scheduling with calendar integration</span>
                </li>
                <li className="flex items-start">
                  <span className="text-green-500 mr-3 mt-1">✓</span>
                  <span className="text-gray-700">Automated progress updates and reminders</span>
                </li>
                <li className="flex items-start">
                  <span className="text-green-500 mr-3 mt-1">✓</span>
                  <span className="text-gray-700">Real-time analytics and profitability tracking</span>
                </li>
                <li className="flex items-start">
                  <span className="text-green-500 mr-3 mt-1">✓</span>
                  <span className="text-gray-700">40% increase in project efficiency</span>
                </li>
              </ul>
            </div>
          </div>
        </FadeInSection>

        {/* ROI Calculator CTA */}
        <FadeInSection delay={1400}>
          <div className="bg-gradient-to-r from-orange-500 to-red-600 rounded-2xl p-8 text-center text-white">
            <h3 className="text-2xl font-bold mb-4">Calculate Your Construction ROI</h3>
            <p className="text-orange-100 mb-6 max-w-2xl mx-auto">
              See how much time and money you could save with automated construction processes
            </p>
            <a 
              href="/roi-calculator"
              className="inline-block bg-white text-orange-600 hover:bg-orange-50 px-8 py-3 rounded-lg font-semibold transition-colors duration-300"
            >
              Calculate Your ROI →
            </a>
          </div>
        </FadeInSection>
      </div>
    </section>
  );
}
