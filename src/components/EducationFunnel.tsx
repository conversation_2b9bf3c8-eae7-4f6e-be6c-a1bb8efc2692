import React from 'react';
import FadeInSection from './FadeInSection';

interface FunnelStage {
  id: number;
  title: string;
  description: string;
  icon: string;
  features: string[];
  conversionRate: number;
  students: number;
  qualified: number;
}

const funnelStages: FunnelStage[] = [
  {
    id: 1,
    title: 'Lead Generation',
    description: 'Capture student inquiries and initial interest',
    icon: '🎯',
    features: [
      'Multi-channel lead capture (website, social media, referrals)',
      'Automatic lead qualification and course interest assessment',
      'Instant notification to admissions team'
    ],
    conversionRate: 100,
    students: 100,
    qualified: 85
  },
  {
    id: 2,
    title: 'Initial Consultation',
    description: 'Assess student needs and course requirements',
    icon: '📋',
    features: [
      'Automated consultation scheduling',
      'Student needs assessment questionnaires',
      'Course recommendation based on goals and level'
    ],
    conversionRate: 85,
    students: 85,
    qualified: 70
  },
  {
    id: 3,
    title: 'Trial Lesson',
    description: 'Provide sample lesson experience',
    icon: '📚',
    features: [
      'Automated trial lesson booking and confirmation',
      'Teacher assignment and preparation notifications',
      'Post-trial feedback collection and follow-up'
    ],
    conversionRate: 70,
    students: 70,
    qualified: 55
  },
  {
    id: 4,
    title: 'Course Selection',
    description: 'Choose appropriate course and schedule',
    icon: '📄',
    features: [
      'Course catalog presentation and comparison',
      'Schedule coordination and availability checking',
      'Pricing discussion and payment plan setup'
    ],
    conversionRate: 55,
    students: 55,
    qualified: 45
  },
  {
    id: 5,
    title: 'Enrollment & Payment',
    description: 'Complete registration and payment process',
    icon: '💳',
    features: [
      'Automated enrollment documentation and contracts',
      'Payment processing and installment plan setup',
      'Welcome package and course material delivery'
    ],
    conversionRate: 45,
    students: 45,
    qualified: 40
  },
  {
    id: 6,
    title: 'Student Success',
    description: 'Ongoing support and retention',
    icon: '✅',
    features: [
      'Progress tracking and milestone celebrations',
      'Automated attendance monitoring and follow-up',
      'Course completion certificates and next level recommendations'
    ],
    conversionRate: 40,
    students: 40,
    qualified: 40
  }
];

export default function EducationFunnel() {
  return (
    <section className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <FadeInSection className="text-center mb-16">
          <div className="inline-block px-4 py-2 mb-6 bg-blue-100 text-blue-600 rounded-full text-sm font-semibold">
            🔄 Automated Workflow
          </div>
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Complete Education Process Automation
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            From initial inquiry to student success - every step optimized with Kommo CRM
          </p>
        </FadeInSection>

        <div className="space-y-8">
          {funnelStages.map((stage, index) => (
            <div key={stage.id} className="relative">
              <FadeInSection delay={index * 200}>
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 items-center">
                  {/* Stage Content */}
                  <div className="lg:col-span-2">
                    <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-6 border border-blue-100">
                      <div className="flex items-start mb-4">
                        <div className="w-12 h-12 bg-blue-500 rounded-lg flex items-center justify-center mr-4 flex-shrink-0">
                          <span className="text-2xl">{stage.icon}</span>
                        </div>
                        <div className="flex-1">
                          <h3 className="text-xl font-bold text-gray-900 mb-2">{stage.title}</h3>
                          <p className="text-gray-600 mb-4">{stage.description}</p>
                        </div>
                      </div>
                      
                      <div className="ml-16">
                        <h4 className="font-semibold text-gray-900 mb-3">Automation Features:</h4>
                        <ul className="space-y-2">
                          {stage.features.map((feature, featureIndex) => (
                            <li key={featureIndex} className="flex items-start">
                              <div className="w-5 h-5 bg-green-100 rounded-full flex items-center justify-center mr-3 mt-0.5 flex-shrink-0">
                                <span className="text-green-600 text-xs font-bold">✓</span>
                              </div>
                              <span className="text-gray-700 text-sm">{feature}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  </div>

                  {/* Conversion Metrics */}
                  <div className="lg:col-span-1">
                    <div className="bg-white rounded-xl p-6 shadow-lg border border-gray-200">
                      <div className="text-center mb-4">
                        <div className="text-sm font-medium text-gray-500 mb-1">Conversion Rate</div>
                        <div className="text-2xl font-bold text-blue-600">{stage.conversionRate}%</div>
                      </div>
                      
                      <div className="space-y-3">
                        <div className="flex justify-between items-center">
                          <div className="text-center">
                            <div className="text-lg font-bold text-gray-900">{stage.students}</div>
                            <div className="text-xs text-gray-500">Students</div>
                          </div>
                          <div className="flex-1 mx-4">
                            <div className="h-2 bg-gray-200 rounded-full overflow-hidden">
                              <div 
                                className="h-full bg-gradient-to-r from-blue-500 to-purple-500 rounded-full transition-all duration-1000"
                                style={{ width: `${stage.conversionRate}%` }}
                              ></div>
                            </div>
                          </div>
                          <div className="text-center">
                            <div className="text-lg font-bold text-purple-600">{stage.qualified}</div>
                            <div className="text-xs text-gray-500">Qualified</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </FadeInSection>

              {/* Arrow connector */}
              {index < funnelStages.length - 1 && (
                <div className="flex justify-center my-6">
                  <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                    <svg className="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                    </svg>
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>

        {/* Summary Results */}
        <FadeInSection delay={1200} className="mt-16">
          <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl p-8 text-white text-center">
            <h3 className="text-2xl font-bold mb-6">Complete Automation Results</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div>
                <div className="text-3xl font-bold text-yellow-300 mb-2">35%</div>
                <div className="text-blue-100">More Student Enrollment</div>
              </div>
              <div>
                <div className="text-3xl font-bold text-yellow-300 mb-2">50%</div>
                <div className="text-blue-100">Less Administrative Work</div>
              </div>
              <div>
                <div className="text-3xl font-bold text-yellow-300 mb-2">3x</div>
                <div className="text-blue-100">Faster Enrollment Process</div>
              </div>
            </div>
          </div>
        </FadeInSection>
      </div>
    </section>
  );
}
