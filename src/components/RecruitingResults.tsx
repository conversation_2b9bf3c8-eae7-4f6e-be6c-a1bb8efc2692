'use client';

import React from 'react';
import FadeInSection from './FadeInSection';

interface ResultMetric {
  value: string;
  label: string;
  description: string;
  color: 'green' | 'blue' | 'purple' | 'orange';
  icon: string;
}

const results: ResultMetric[] = [
  {
    value: '+45%',
    label: 'More Successful Placements',
    description: 'Improved candidate matching and client satisfaction',
    color: 'green',
    icon: '🎯'
  },
  {
    value: '60%',
    label: 'Less Manual Work',
    description: 'Automated screening, scheduling, and follow-ups',
    color: 'blue',
    icon: '⚡'
  },
  {
    value: '3x',
    label: 'Faster Time-to-Fill',
    description: 'Streamlined processes and better candidate tracking',
    color: 'purple',
    icon: '🚀'
  },
  {
    value: '98%',
    label: 'Client Satisfaction',
    description: 'Consistent communication and quality placements',
    color: 'orange',
    icon: '⭐'
  }
];

const testimonials = [
  {
    quote: "Kommo CRM transformed our recruiting process. We're placing 45% more candidates and our team is much more efficient.",
    author: "<PERSON>",
    position: "Recruiting Director",
    company: "TalentPro Recruiting",
    avatar: "👩‍💼"
  },
  {
    quote: "The automated candidate matching and client communication features have transformed how we work. Our recruiters are more productive and clients are happier.",
    author: "<PERSON>",
    position: "Agency Owner",
    company: "Elite Talent Solutions",
    avatar: "👨‍💼"
  },
  {
    quote: "Integration with business tools and automatic candidate management saved us 15+ hours per week. ROI was achieved in just 6 weeks.",
    author: "Lisa Rodriguez",
    position: "Operations Manager",
    company: "Strategic Staffing Group",
    avatar: "👩‍💻"
  }
];

const getColorClasses = (color: ResultMetric['color']) => {
  const colorMap = {
    green: 'bg-green-100 text-green-800 border-green-200',
    blue: 'bg-blue-100 text-blue-800 border-blue-200',
    purple: 'bg-purple-100 text-purple-800 border-purple-200',
    orange: 'bg-orange-100 text-orange-800 border-orange-200'
  };
  return colorMap[color];
};

export default function RecruitingResults() {
  return (
    <section className="py-20 bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <FadeInSection className="text-center mb-16">
          <div className="inline-block px-4 py-2 mb-6 bg-blue-100 text-blue-600 rounded-full text-sm font-semibold">
            📈 Proven Results
          </div>
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Real Results from Recruiting Agencies
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            See how Kommo CRM has transformed recruitment processes for agencies like yours
          </p>
        </FadeInSection>

        {/* Results Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-20">
          {results.map((result, index) => (
            <FadeInSection key={index} delay={index * 150}>
              <div className={`p-6 rounded-xl border-2 text-center hover:shadow-lg transition-shadow duration-300 ${getColorClasses(result.color)}`}>
                <div className="text-4xl mb-3">{result.icon}</div>
                <div className="text-3xl font-bold mb-2">{result.value}</div>
                <div className="font-semibold mb-2">{result.label}</div>
                <div className="text-sm opacity-80">{result.description}</div>
              </div>
            </FadeInSection>
          ))}
        </div>


        {/* Before/After Comparison */}
        <FadeInSection delay={800} className="mt-20">
          <div className="bg-white rounded-2xl shadow-xl overflow-hidden">
            <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white text-center py-6">
              <h3 className="text-2xl font-bold">Before vs After Kommo CRM</h3>
            </div>
            
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-0">
              {/* Before */}
              <div className="p-8 bg-red-50">
                <h4 className="text-xl font-bold text-red-800 mb-6 flex items-center">
                  <span className="mr-2">❌</span>
                  Before: Manual Recruiting
                </h4>
                <ul className="space-y-3 text-gray-700">
                  <li className="flex items-start">
                    <span className="text-red-500 mr-3 mt-1">✗</span>
                    <span>Spreadsheet-based candidate tracking</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-red-500 mr-3 mt-1">✗</span>
                    <span>Manual interview scheduling coordination</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-red-500 mr-3 mt-1">✗</span>
                    <span>Inconsistent client communication</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-red-500 mr-3 mt-1">✗</span>
                    <span>No performance analytics or insights</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-red-500 mr-3 mt-1">✗</span>
                    <span>Lost candidates due to poor follow-up</span>
                  </li>
                </ul>
              </div>

              {/* After */}
              <div className="p-8 bg-green-50">
                <h4 className="text-xl font-bold text-green-800 mb-6 flex items-center">
                  <span className="mr-2">✅</span>
                  After: Automated CRM System
                </h4>
                <ul className="space-y-3 text-gray-700">
                  <li className="flex items-start">
                    <span className="text-green-500 mr-3 mt-1">✓</span>
                    <span>Centralized candidate and client database</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-green-500 mr-3 mt-1">✓</span>
                    <span>Automated scheduling with Calendly integration</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-green-500 mr-3 mt-1">✓</span>
                    <span>Automated follow-up sequences and reminders</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-green-500 mr-3 mt-1">✓</span>
                    <span>Real-time analytics and performance tracking</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-green-500 mr-3 mt-1">✓</span>
                    <span>45% increase in successful placements</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </FadeInSection>

        {/* ROI Calculator CTA */}
        <FadeInSection delay={1000} className="mt-16 text-center">
          <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl p-8 text-white">
            <h3 className="text-2xl font-bold mb-4">Calculate Your Recruiting ROI</h3>
            <p className="text-blue-100 mb-6 max-w-2xl mx-auto">
              See how much time and money you could save with automated recruiting processes
            </p>
            <a
              href="/roi-calculator"
              className="inline-block bg-white text-blue-600 hover:bg-blue-50 px-8 py-4 rounded-lg font-semibold text-lg shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
            >
              Calculate Your ROI →
            </a>
          </div>
        </FadeInSection>
      </div>
    </section>
  );
}
