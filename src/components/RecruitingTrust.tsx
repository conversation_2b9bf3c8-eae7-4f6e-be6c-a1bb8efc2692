'use client';

import React from 'react';
import FadeInSection from './FadeInSection';
import Image from 'next/image';

const partners = [
  { name: 'Kommo Certified Partner', logo: '/Crtifikat.webp', verified: true },
  { name: 'Calendly Integration Expert', logo: '📅', verified: true },
  { name: 'Zoom Integration Partner', logo: '🎥', verified: true },
  { name: 'Make.com Integration Expert', logo: '🔗', verified: true }
];

const achievements = [
  {
    metric: '320+',
    label: 'Recruiting Projects',
    description: 'Successfully implemented CRM solutions',
    icon: '🚀'
  },
  {
    metric: '10+',
    label: 'Years Experience',
    description: 'In recruiting technology solutions',
    icon: '⭐'
  },
  {
    metric: '4000+',
    label: 'Recruiters Trained',
    description: 'On Kommo CRM best practices',
    icon: '👥'
  },
  {
    metric: '98%',
    label: 'Client Satisfaction',
    description: 'Based on post-implementation surveys',
    icon: '💯'
  }
];

const clientLogos = [
  { name: 'TalentPro Recruiting', logo: '🎯' },
  { name: 'Elite Staffing Solutions', logo: '⭐' },
  { name: 'Strategic Talent Group', logo: '🚀' },
  { name: 'Executive Search Partners', logo: '💼' },
  { name: 'Tech Recruiting Specialists', logo: '💻' },
  { name: 'Healthcare Staffing Pro', logo: '🏥' }
];

const certifications = [
  {
    name: 'Kommo CRM Expert',
    description: 'Advanced CRM implementation and optimization',
    icon: '🎯',
    color: 'bg-blue-100 text-blue-600'
  },
  {
    name: 'Calendly Integration Specialist',
    description: 'Expert-level scheduling and appointment automation',
    icon: '📅',
    color: 'bg-purple-100 text-purple-600'
  },
  {
    name: 'Zoom Integration Certified',
    description: 'Meeting automation and client communication expertise',
    icon: '🎥',
    color: 'bg-orange-100 text-orange-600'
  },
  {
    name: 'Make.com Partner',
    description: 'Advanced automation and integration solutions',
    icon: '🔗',
    color: 'bg-green-100 text-green-600'
  }
];

export default function RecruitingTrust() {
  return (
    <section className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Certifications Section */}
        <FadeInSection>
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Certified Recruiting CRM Experts
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Trust your recruiting CRM implementation to certified professionals with proven expertise
            </p>
          </div>
        </FadeInSection>

        {/* Main Certification Badge */}
        <FadeInSection delay={200}>
          <div className="text-center mb-12">
            <div className="inline-block bg-white rounded-lg p-6 shadow-xl max-w-3xl mx-auto">
              <img
                src="/Crtifikat.webp"
                alt="Kommo Partnership Certificate"
                className="w-full max-w-2xl mx-auto rounded-lg border border-gray-200"
              />
            </div>
          </div>
        </FadeInSection>





        
      </div>
    </section>
  );
}
