import React from 'react';
import { cn } from '@/lib/utils';
import FadeInSection from './FadeInSection';

interface ResultMetric {
  value: string;
  label: string;
  description: string;
  color: 'blue' | 'purple' | 'indigo' | 'orange';
  icon: string;
}

const results: ResultMetric[] = [
  {
    value: '+180%',
    label: 'B2B Lead Quality',
    description: 'Better qualified prospects through automation',
    color: 'blue',
    icon: '🎯'
  },
  {
    value: '65%',
    label: 'Faster Sales Cycles',
    description: 'Reduced time from lead to close',
    color: 'purple',
    icon: '⚡'
  },
  {
    value: '-45%',
    label: 'Administrative Tasks',
    description: 'Less manual work on follow-ups and reporting',
    color: 'indigo',
    icon: '📊'
  },
  {
    value: '+150%',
    label: 'Sales Team Productivity',
    description: 'Better pipeline management and tracking',
    color: 'orange',
    icon: '💰'
  }
];

const testimonials = [
  {
    quote: "Setmee implemented Kommo CRM perfectly for our B2B sales. Lead management became seamless, and our sales team productivity increased by 85% with automated workflows.",
    author: "<PERSON>",
    position: "Sales Director",
    company: "Industrial Equipment Solutions",
    avatar: "👨‍💼"
  },
  {
    quote: "The CRM implementation by <PERSON><PERSON> transformed our B2B sales process. We now track complex sales cycles effectively and our closing rates improved significantly.",
    author: "<PERSON> Martinez",
    position: "VP of Sales",
    company: "Professional Services Group",
    avatar: "👩‍💼"
  },
  {
    quote: "Excellent B2B CRM setup and integration work. Setmee connected all our business tools with Kommo, and now our sales process is fully automated from lead to customer.",
    author: "Michael Thompson",
    position: "Business Development Manager",
    company: "Technology Consulting Firm",
    avatar: "👨‍💻"
  }
];

const getColorClasses = (color: string) => {
  const colorMap = {
    blue: {
      bg: 'bg-blue-50',
      border: 'border-blue-200',
      icon: 'bg-blue-100 text-blue-600',
      value: 'text-blue-600'
    },
    purple: {
      bg: 'bg-purple-50',
      border: 'border-purple-200',
      icon: 'bg-purple-100 text-purple-600',
      value: 'text-purple-600'
    },
    indigo: {
      bg: 'bg-indigo-50',
      border: 'border-indigo-200',
      icon: 'bg-indigo-100 text-indigo-600',
      value: 'text-indigo-600'
    },
    orange: {
      bg: 'bg-orange-50',
      border: 'border-orange-200',
      icon: 'bg-orange-100 text-orange-600',
      value: 'text-orange-600'
    }
  };
  return colorMap[color as keyof typeof colorMap] || colorMap.blue;
};

const EcommerceResults: React.FC = () => {
  return (
    <div className="space-y-16">
      {/* Results Metrics */}
      <FadeInSection>
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            CRM Implementation Results for B2B Companies
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            See the measurable impact our Kommo CRM implementation delivers for B2B companies and corporate sales teams
          </p>
        </div>
      </FadeInSection>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {results.map((result, index) => {
          const colors = getColorClasses(result.color);
          return (
            <FadeInSection key={index} delay={index * 150}>
              <div className={cn(
                'p-6 rounded-xl border-2 transition-all duration-300 hover:shadow-lg hover:-translate-y-1',
                colors.bg,
                colors.border
              )}>
                <div className={cn(
                  'w-12 h-12 rounded-full flex items-center justify-center text-2xl mb-4',
                  colors.icon
                )}>
                  {result.icon}
                </div>
                <div className={cn('text-3xl font-bold mb-2', colors.value)}>
                  {result.value}
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  {result.label}
                </h3>
                <p className="text-gray-600 text-sm">
                  {result.description}
                </p>
              </div>
            </FadeInSection>
          );
        })}
      </div>


{/* Industry-Specific Results */}
      <FadeInSection delay={1600}>
        <div className="bg-white rounded-2xl p-8 border border-gray-100 shadow-lg">
          <div className="text-center mb-8">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              Industry-Specific Performance Metrics
            </h3>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Results vary by industry, but all our B2B clients see significant improvements
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="text-center p-6 bg-gray-50 rounded-xl">
              <div className="text-3xl mb-3">🏭</div>
              <h4 className="font-semibold text-gray-900 mb-2">Industrial Equipment</h4>
              <div className="space-y-2">
                <div className="text-lg font-bold text-blue-600">+250%</div>
                <div className="text-sm text-gray-600">Lead Quality</div>
                <div className="text-lg font-bold text-purple-600">-60%</div>
                <div className="text-sm text-gray-600">Sales Cycle Time</div>
              </div>
            </div>
            
            <div className="text-center p-6 bg-gray-50 rounded-xl">
              <div className="text-3xl mb-3">⚕️</div>
              <h4 className="font-semibold text-gray-900 mb-2">Medical Devices</h4>
              <div className="space-y-2">
                <div className="text-lg font-bold text-blue-600">+180%</div>
                <div className="text-sm text-gray-600">Qualified Leads</div>
                <div className="text-lg font-bold text-purple-600">+320%</div>
                <div className="text-sm text-gray-600">Deal Value</div>
              </div>
            </div>
            
            <div className="text-center p-6 bg-gray-50 rounded-xl">
              <div className="text-3xl mb-3">🔬</div>
              <h4 className="font-semibold text-gray-900 mb-2">Scientific Equipment</h4>
              <div className="space-y-2">
                <div className="text-lg font-bold text-blue-600">+200%</div>
                <div className="text-sm text-gray-600">Demo Requests</div>
                <div className="text-lg font-bold text-purple-600">+150%</div>
                <div className="text-sm text-gray-600">Conversion Rate</div>
              </div>
            </div>
            
            <div className="text-center p-6 bg-gray-50 rounded-xl">
              <div className="text-3xl mb-3">🏗️</div>
              <h4 className="font-semibold text-gray-900 mb-2">Construction Materials</h4>
              <div className="space-y-2">
                <div className="text-lg font-bold text-blue-600">+300%</div>
                <div className="text-sm text-gray-600">Quote Accuracy</div>
                <div className="text-lg font-bold text-purple-600">-70%</div>
                <div className="text-sm text-gray-600">Admin Time</div>
              </div>
            </div>
          </div>
        </div>
      </FadeInSection>

      {/* ROI Calculator Preview */}
      <FadeInSection delay={1800}>
        <div className="bg-gradient-to-br from-indigo-50 to-purple-50 rounded-2xl p-8">
          <div className="text-center mb-8">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              Calculate Your B2B ROI
            </h3>
            <p className="text-gray-600 max-w-2xl mx-auto">
              See the potential impact of CRM automation on your complex B2B sales process
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center p-6 bg-white rounded-xl shadow-sm">
              <div className="text-2xl font-bold text-gray-900 mb-2">40 hrs/week</div>
              <div className="text-sm text-gray-600 mb-4">Time saved on manual processes</div>
              <div className="text-lg font-semibold text-blue-600">= $8,000/month</div>
              <div className="text-xs text-gray-500">at $50/hour value</div>
            </div>
            
            <div className="text-center p-6 bg-white rounded-xl shadow-sm">
              <div className="text-2xl font-bold text-gray-900 mb-2">5x more</div>
              <div className="text-sm text-gray-600 mb-4">Qualified leads processed</div>
              <div className="text-lg font-semibold text-blue-600">= $50,000/month</div>
              <div className="text-xs text-gray-500">additional revenue</div>
            </div>
            
            <div className="text-center p-6 bg-white rounded-xl shadow-sm">
              <div className="text-2xl font-bold text-gray-900 mb-2">65%</div>
              <div className="text-sm text-gray-600 mb-4">Faster sales cycles</div>
              <div className="text-lg font-semibold text-blue-600">= Faster cash flow</div>
              <div className="text-xs text-gray-500">and growth</div>
            </div>
          </div>

          <div className="text-center mt-8">
            <div className="inline-block px-6 py-3 bg-blue-600 text-white rounded-lg font-semibold hover:bg-blue-700 transition-colors cursor-pointer">
              Get Your Custom ROI Analysis
            </div>
          </div>
        </div>
      </FadeInSection>
    </div>
  );
};

export default EcommerceResults;
