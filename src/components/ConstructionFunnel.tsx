'use client';

import React from 'react';
import FadeInSection from './FadeInSection';
import { cn } from '@/lib/utils';

interface FunnelStage {
  id: number;
  title: string;
  description: string;
  icon: string;
  automation: string[];
  color: string;
  percentage: number;
}

const funnelStages: FunnelStage[] = [
  {
    id: 1,
    title: 'Lead Generation',
    description: 'Capture project inquiries and initial client contact',
    icon: '🎯',
    automation: [
      'Multi-channel lead capture (website, referrals, phone)',
      'Automatic lead qualification and project type assessment',
      'Instant notification to project managers'
    ],
    color: 'from-orange-500 to-orange-600',
    percentage: 100
  },
  {
    id: 2,
    title: 'Initial Consultation',
    description: 'Assess project requirements and client needs',
    icon: '📋',
    automation: [
      'Automated consultation scheduling',
      'Project requirement questionnaires',
      'Site visit coordination and reminders'
    ],
    color: 'from-yellow-500 to-yellow-600',
    percentage: 80
  },
  {
    id: 3,
    title: 'Proposal & Estimation',
    description: 'Create detailed project proposals and cost estimates',
    icon: '📊',
    automation: [
      'Automated proposal template generation',
      'Cost calculation and material estimation',
      'Proposal delivery and follow-up sequences'
    ],
    color: 'from-amber-500 to-amber-600',
    percentage: 65
  },
  {
    id: 4,
    title: 'Contract & Planning',
    description: 'Finalize contracts and project planning',
    icon: '📄',
    automation: [
      'Contract generation and e-signature workflows',
      'Project timeline creation and milestone setup',
      'Permit and documentation tracking'
    ],
    color: 'from-orange-600 to-red-600',
    percentage: 45
  },
  {
    id: 5,
    title: 'Project Execution',
    description: 'Manage ongoing construction work and progress',
    icon: '🏗️',
    automation: [
      'Daily progress tracking and photo documentation',
      'Automated client progress updates',
      'Material delivery and crew scheduling coordination'
    ],
    color: 'from-red-500 to-red-600',
    percentage: 40
  },
  {
    id: 6,
    title: 'Completion & Follow-up',
    description: 'Project completion, handover, and maintenance',
    icon: '✅',
    automation: [
      'Final inspection scheduling and documentation',
      'Automated warranty and maintenance reminders',
      'Client satisfaction surveys and referral requests'
    ],
    color: 'from-green-500 to-green-600',
    percentage: 35
  }
];

export default function ConstructionFunnel() {
  return (
    <section className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <FadeInSection className="text-center mb-16">
          <div className="inline-block px-4 py-2 mb-6 bg-orange-100 text-orange-600 rounded-full text-sm font-semibold">
            🔄 Automated Workflow
          </div>
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Complete Construction Process Automation
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            From initial inquiry to project completion - every step optimized with Kommo CRM
          </p>
        </FadeInSection>

        <div className="space-y-16">
          {funnelStages.map((stage, index) => (
            <FadeInSection key={stage.id} delay={index * 200}>
              <div className={cn(
                'relative flex flex-col lg:flex-row items-center gap-8',
                index % 2 === 0 ? 'lg:flex-row' : 'lg:flex-row-reverse'
              )}>
                {/* Stage Content */}
                <div className="flex-1 max-w-lg">
                  <div className="bg-white rounded-xl shadow-lg p-6 border border-gray-100 hover:shadow-xl transition-shadow duration-300">
                    <div className="flex items-center mb-4">
                      <div className={cn(
                        'w-12 h-12 rounded-full flex items-center justify-center text-2xl text-white mr-4',
                        `bg-gradient-to-r ${stage.color}`
                      )}>
                        {stage.icon}
                      </div>
                      <div>
                        <h3 className="text-xl font-semibold text-gray-900">{stage.title}</h3>
                        <p className="text-gray-600 text-sm">{stage.description}</p>
                      </div>
                    </div>
                    
                    <div className="mb-4">
                      <h4 className="text-sm font-semibold text-gray-900 mb-2">Automation Features:</h4>
                      <ul className="space-y-2">
                        {stage.automation.map((feature, idx) => (
                          <li key={idx} className="flex items-start text-sm text-gray-600">
                            <span className="text-green-500 mr-2 mt-0.5">✓</span>
                            {feature}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>

                {/* Conversion Visualization */}
                <div className="flex-1 max-w-sm">
                  <div className="bg-gray-50 rounded-xl p-6 text-center">
                    <div className="text-sm font-medium text-gray-600 mb-2">Conversion Rate</div>
                    <div className="text-2xl font-bold text-orange-600 mb-4">{stage.percentage}%</div>
                    
                    <div className="flex justify-between items-center text-xs text-gray-500">
                      <div className="text-center">
                        <div className="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center text-orange-600 font-bold mx-auto mb-1">
                          {stage.percentage}
                        </div>
                        <div>Projects</div>
                      </div>
                      <div className="flex-1 mx-4">
                        <div className="h-2 bg-gray-200 rounded-full overflow-hidden">
                          <div 
                            className={cn('h-full bg-gradient-to-r', stage.color)}
                            style={{ width: `${stage.percentage}%` }}
                          ></div>
                        </div>
                      </div>
                      <div className="text-center">
                        <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center text-green-600 font-bold mx-auto mb-1">
                          {index < funnelStages.length - 1 ? funnelStages[index + 1].percentage : stage.percentage}
                        </div>
                        <div>Qualified</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </FadeInSection>
          ))}
        </div>

        {/* Summary Results */}
        <FadeInSection delay={1200}>
          <div className="mt-16 bg-gradient-to-r from-orange-500 to-red-600 rounded-2xl p-8 text-white text-center">
            <h3 className="text-2xl font-bold mb-6">Complete Automation Results</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div>
                <div className="text-3xl font-bold mb-2">40%</div>
                <div className="text-orange-100">More Project Efficiency</div>
              </div>
              <div>
                <div className="text-3xl font-bold mb-2">55%</div>
                <div className="text-orange-100">Less Administrative Work</div>
              </div>
              <div>
                <div className="text-3xl font-bold mb-2">3x</div>
                <div className="text-orange-100">Faster Project Setup</div>
              </div>
            </div>
          </div>
        </FadeInSection>
      </div>
    </section>
  );
}
