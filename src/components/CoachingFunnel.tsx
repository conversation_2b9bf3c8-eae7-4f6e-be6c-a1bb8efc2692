import React from 'react';
import { cn } from '@/lib/utils';
import FadeInSection from './FadeInSection';

interface FunnelStage {
  id: number;
  title: string;
  description: string;
  icon: string;
  automation: string[];
  color: string;
  percentage: number;
}

const funnelStages: FunnelStage[] = [
  {
    id: 1,
    title: 'Lead Magnet & Discovery',
    description: 'Attract potential clients with valuable free content',
    icon: '🧲',
    automation: [
      'Automated lead magnet delivery (ebooks, guides, assessments)',
      'Welcome email sequence with value-driven content',
      'Lead scoring based on engagement and downloads'
    ],
    color: 'from-green-500 to-green-600',
    percentage: 100
  },
  {
    id: 2,
    title: 'Nurturing & Education',
    description: 'Build trust through educational content and mini-lessons',
    icon: '📚',
    automation: [
      'Automated email sequence delivery',
      'Behavioral triggers based on content consumption',
      'Personalized content recommendations'
    ],
    color: 'from-blue-500 to-blue-600',
    percentage: 65
  },
  {
    id: 3,
    title: 'Discovery Call Booking',
    description: 'Convert engaged leads into consultation calls',
    icon: '📞',
    automation: [
      'Automated calendar booking system',
      'Pre-call questionnaire and preparation materials',
      'Reminder sequences for scheduled calls'
    ],
    color: 'from-purple-500 to-purple-600',
    percentage: 35
  },
  {
    id: 4,
    title: 'Consultation & Assessment',
    description: 'Understand client needs and present solutions',
    icon: '🎯',
    automation: [
      'Automated follow-up after discovery calls',
      'Proposal generation based on call notes',
      'Custom pricing calculator integration'
    ],
    color: 'from-pink-500 to-pink-600',
    percentage: 25
  },
  {
    id: 5,
    title: 'Enrollment & Onboarding',
    description: 'Convert prospects into paying clients',
    icon: '✅',
    automation: [
      'Automated payment processing and invoicing',
      'Welcome sequence for new clients',
      'Service or program access provisioning'
    ],
    color: 'from-orange-500 to-orange-600',
    percentage: 15
  },
  {
    id: 6,
    title: 'Delivery & Engagement',
    description: 'Ensure client success and maintain engagement',
    icon: '🚀',
    automation: [
      'Client progress tracking and milestone celebrations',
      'Automated check-ins and support messages',
      'Success celebrations and next-step recommendations'
    ],
    color: 'from-emerald-500 to-emerald-600',
    percentage: 12
  }
];

const CoachingFunnel: React.FC = () => {
  return (
    <div className="space-y-12">
      <FadeInSection>
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Coaching & Consulting Sales Funnel with Automation
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            See how Kommo CRM automates every stage of your coaching business, 
            from lead generation to client success and business growth
          </p>
        </div>
      </FadeInSection>

      {/* Funnel Visualization */}
      <div className="relative">
        {/* Connection Lines */}
        <div className="hidden lg:block absolute left-1/2 top-0 bottom-0 w-0.5 bg-gradient-to-b from-green-200 via-purple-200 to-emerald-200 transform -translate-x-1/2"></div>

        <div className="space-y-8">
          {funnelStages.map((stage, index) => (
            <FadeInSection key={stage.id} delay={index * 200}>
              <div className={cn(
                'relative flex flex-col lg:flex-row items-center gap-8',
                index % 2 === 0 ? 'lg:flex-row' : 'lg:flex-row-reverse'
              )}>
                {/* Stage Content */}
                <div className="flex-1 max-w-lg">
                  <div className="bg-white rounded-xl shadow-lg p-6 border border-gray-100 hover:shadow-xl transition-shadow duration-300">
                    <div className="flex items-center mb-4">
                      <div className={cn(
                        'w-12 h-12 rounded-full flex items-center justify-center text-2xl text-white mr-4',
                        `bg-gradient-to-r ${stage.color}`
                      )}>
                        {stage.icon}
                      </div>
                      <div>
                        <h3 className="text-xl font-bold text-gray-900">{stage.title}</h3>
                        <p className="text-gray-600 text-sm">{stage.description}</p>
                      </div>
                    </div>

                    <div className="space-y-3">
                      <h4 className="font-semibold text-gray-800 text-sm uppercase tracking-wide">
                        Automation Features:
                      </h4>
                      <ul className="space-y-2">
                        {stage.automation.map((feature, featureIndex) => (
                          <li key={featureIndex} className="flex items-start text-sm text-gray-700">
                            <span className="text-green-500 mr-2 mt-0.5">✓</span>
                            <span>{feature}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>

                {/* Conversion Rate Display */}
                <div className="flex-shrink-0">
                  <div className="relative">
                    {/* Circular Progress */}
                    <div className="w-32 h-32 relative">
                      <svg className="w-32 h-32 transform -rotate-90" viewBox="0 0 120 120">
                        {/* Background circle */}
                        <circle
                          cx="60"
                          cy="60"
                          r="50"
                          fill="none"
                          stroke="#e5e7eb"
                          strokeWidth="8"
                        />
                        {/* Progress circle */}
                        <circle
                          cx="60"
                          cy="60"
                          r="50"
                          fill="none"
                          stroke="url(#coachingGradient)"
                          strokeWidth="8"
                          strokeLinecap="round"
                          strokeDasharray={`${2 * Math.PI * 50}`}
                          strokeDashoffset={`${2 * Math.PI * 50 * (1 - stage.percentage / 100)}`}
                          className="transition-all duration-1000"
                        />
                        {/* Gradient definition */}
                        <defs>
                          <linearGradient id="coachingGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" stopColor="#10b981" />
                            <stop offset="100%" stopColor="#14b8a6" />
                          </linearGradient>
                        </defs>
                      </svg>

                      {/* Center content */}
                      <div className="absolute inset-0 flex flex-col items-center justify-center">
                        <div className="text-2xl font-bold text-emerald-600">{stage.percentage}%</div>
                        <div className="text-xs text-gray-500 text-center">Conversion</div>
                      </div>
                    </div>

                    {/* Stage number badge */}
                    <div className="absolute -top-2 -right-2 w-8 h-8 bg-emerald-500 text-white rounded-full flex items-center justify-center text-sm font-bold shadow-lg">
                      {stage.id}
                    </div>
                  </div>
                </div>
              </div>
            </FadeInSection>
          ))}
        </div>
      </div>

      {/* Key Benefits */}
      <FadeInSection delay={1200}>
        <div className="bg-gradient-to-br from-green-50 to-emerald-50 rounded-2xl p-8 mt-16">
          <div className="text-center mb-8">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              Why This Funnel Works for Coaches & Consultants
            </h3>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center text-2xl text-green-600 mx-auto mb-4">
                🎓
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">Education-First Approach</h4>
              <p className="text-gray-600 text-sm">
                Builds trust through valuable content before selling, perfect for knowledge-based businesses
              </p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center text-2xl text-blue-600 mx-auto mb-4">
                💝
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">Relationship Building</h4>
              <p className="text-gray-600 text-sm">
                Focuses on long-term relationships and client success rather than quick sales
              </p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center text-2xl text-purple-600 mx-auto mb-4">
                📈
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">Scalable Growth</h4>
              <p className="text-gray-600 text-sm">
                Automated systems allow you to serve more clients without proportional time increase
              </p>
            </div>
          </div>
        </div>
      </FadeInSection>

      {/* Success Metrics */}
      <FadeInSection delay={1400}>
        <div className="bg-white rounded-2xl p-8 border border-gray-100 shadow-lg">
          <div className="text-center mb-8">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              Typical Funnel Performance Metrics
            </h3>
            <p className="text-gray-600">
              Industry benchmarks for coaching and consulting funnels
            </p>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            <div className="text-center">
              <div className="text-3xl font-bold text-green-600 mb-2">65%</div>
              <div className="text-sm text-gray-600">Lead Magnet to Nurturing</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-blue-600 mb-2">54%</div>
              <div className="text-sm text-gray-600">Nurturing to Discovery Call</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-purple-600 mb-2">71%</div>
              <div className="text-sm text-gray-600">Call to Consultation</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-orange-600 mb-2">60%</div>
              <div className="text-sm text-gray-600">Consultation to Enrollment</div>
            </div>
          </div>
        </div>
      </FadeInSection>
    </div>
  );
};

export default CoachingFunnel;
