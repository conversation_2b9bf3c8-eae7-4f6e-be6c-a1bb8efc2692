import React from 'react';
import { cn } from '@/lib/utils';
import FadeInSection from './FadeInSection';

interface ResultMetric {
  value: string;
  label: string;
  description: string;
  color: 'cyan' | 'blue' | 'indigo' | 'purple';
  icon: string;
}

const results: ResultMetric[] = [
  {
    value: '+85%',
    label: 'Lead Response Time',
    description: 'Faster lead processing with CRM automation',
    color: 'cyan',
    icon: '🚀'
  },
  {
    value: '40%',
    label: 'Shorter Sales Cycles',
    description: 'Streamlined sales process management',
    color: 'blue',
    icon: '⚡'
  },
  {
    value: '-50%',
    label: 'Manual Tasks Reduction',
    description: 'Automated workflows and integrations',
    color: 'indigo',
    icon: '🛡️'
  },
  {
    value: '+120%',
    label: 'Sales Team Productivity',
    description: 'Better pipeline management and tracking',
    color: 'purple',
    icon: '💰'
  }
];

const testimonials = [
  {
    quote: "<PERSON><PERSON> implemented Kommo CRM perfectly for our SaaS business. Lead management became seamless, and our sales team productivity increased by 85% with automated workflows.",
    author: "<PERSON>",
    position: "VP of Sales",
    company: "CloudSync Pro",
    avatar: "👨‍💻"
  },
  {
    quote: "The CRM implementation by <PERSON><PERSON> transformed our sales process. We now track customer lifecycle effectively and our renewal rates improved significantly.",
    author: "<PERSON>",
    position: "Head of Sales Operations",
    company: "DevTools Analytics",
    avatar: "👩‍💼"
  },
  {
    quote: "Excellent CRM setup and integration work. Setmee connected all our tools with Kommo, and now our sales process is fully automated from lead to customer.",
    author: "Michael Park",
    position: "Sales Director",
    company: "API Gateway Solutions",
    avatar: "👨‍🚀"
  }
];

const getColorClasses = (color: string) => {
  const colorMap = {
    cyan: {
      bg: 'bg-cyan-50',
      border: 'border-cyan-200',
      icon: 'bg-cyan-100 text-cyan-600',
      value: 'text-cyan-600'
    },
    blue: {
      bg: 'bg-blue-50',
      border: 'border-blue-200',
      icon: 'bg-blue-100 text-blue-600',
      value: 'text-blue-600'
    },
    indigo: {
      bg: 'bg-indigo-50',
      border: 'border-indigo-200',
      icon: 'bg-indigo-100 text-indigo-600',
      value: 'text-indigo-600'
    },
    purple: {
      bg: 'bg-purple-50',
      border: 'border-purple-200',
      icon: 'bg-purple-100 text-purple-600',
      value: 'text-purple-600'
    }
  };
  return colorMap[color as keyof typeof colorMap] || colorMap.cyan;
};

const SaasResults: React.FC = () => {
  return (
    <div className="space-y-16">
      {/* Results Metrics */}
      <FadeInSection>
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            CRM Implementation Results for SaaS & Tech Companies
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            See the measurable impact our Kommo CRM implementation and automation delivers for SaaS and tech companies
          </p>
        </div>
      </FadeInSection>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {results.map((result, index) => {
          const colors = getColorClasses(result.color);
          return (
            <FadeInSection key={index} delay={index * 150}>
              <div className={cn(
                'p-6 rounded-xl border-2 transition-all duration-300 hover:shadow-lg hover:-translate-y-1',
                colors.bg,
                colors.border
              )}>
                <div className={cn(
                  'w-12 h-12 rounded-full flex items-center justify-center text-2xl mb-4',
                  colors.icon
                )}>
                  {result.icon}
                </div>
                <div className={cn('text-3xl font-bold mb-2', colors.value)}>
                  {result.value}
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  {result.label}
                </h3>
                <p className="text-gray-600 text-sm">
                  {result.description}
                </p>
              </div>
            </FadeInSection>
          );
        })}
      </div>
    </div>
  );
};

export default SaasResults;
