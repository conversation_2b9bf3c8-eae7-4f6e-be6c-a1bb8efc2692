import React from 'react';
import { cn } from '@/lib/utils';
import FadeInSection from './FadeInSection';

interface FunnelStage {
  id: number;
  title: string;
  description: string;
  icon: string;
  automation: string[];
  color: string;
  percentage: number;
}

const funnelStages: FunnelStage[] = [
  {
    id: 1,
    title: 'Lead Generation',
    description: 'Initial inquiry from website, call, or referral',
    icon: '🎯',
    automation: [
      'Automatic lead capture from website forms',
      'SMS confirmation to client within 2 minutes',
      'Instant notification to assigned agent'
    ],
    color: 'from-blue-500 to-blue-600',
    percentage: 100
  },
  {
    id: 2,
    title: 'Qualification',
    description: 'Assess client needs, budget, and timeline',
    icon: '🔍',
    automation: [
      'Automated qualification questionnaire',
      'Budget and timeline assessment forms',
      'Automatic lead scoring and prioritization'
    ],
    color: 'from-indigo-500 to-indigo-600',
    percentage: 75
  },
  {
    id: 3,
    title: 'Requirement Matching',
    description: 'Match client needs with available options',
    icon: '🎯',
    automation: [
      'Automated requirement analysis',
      'Smart client-service matching',
      'Personalized recommendations via email'
    ],
    color: 'from-purple-500 to-purple-600',
    percentage: 60
  },
  {
    id: 4,
    title: 'Meetings & Consultations',
    description: 'Schedule and conduct client meetings',
    icon: '👁️',
    automation: [
      'Online booking system for meetings',
      'Automatic calendar synchronization',
      'Reminder notifications for all parties'
    ],
    color: 'from-pink-500 to-pink-600',
    percentage: 45
  },
  {
    id: 5,
    title: 'Negotiation',
    description: 'Price discussions and offer preparation',
    icon: '🤝',
    automation: [
      'Automated offer document generation',
      'Automated proposal generation',
      'Negotiation timeline tracking'
    ],
    color: 'from-red-500 to-red-600',
    percentage: 30
  },
  {
    id: 6,
    title: 'Contract & Closing',
    description: 'Finalize paperwork and complete transaction',
    icon: '✅',
    automation: [
      'Digital contract management',
      'Automated document collection',
      'Closing timeline and task management'
    ],
    color: 'from-green-500 to-green-600',
    percentage: 25
  }
];

const RealEstateFunnel: React.FC = () => {
  return (
    <div className="space-y-12">
      <FadeInSection>
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Real Estate Sales Funnel with Automation
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            See how Kommo CRM automates every stage of your real estate sales process, 
            from initial lead to closed deal
          </p>
        </div>
      </FadeInSection>

      {/* Desktop Funnel View */}
      <div className="hidden lg:block">
        <div className="relative">
          {/* Funnel Shape Background */}
          <div className="absolute inset-0 flex items-center justify-center">
            <svg width="800" height="600" viewBox="0 0 800 600" className="text-gray-100">
              <path
                d="M100 50 L700 50 L600 550 L200 550 Z"
                fill="currentColor"
                stroke="none"
              />
            </svg>
          </div>

          {/* Funnel Stages */}
          <div className="relative z-10 space-y-8 py-12">
            {funnelStages.map((stage, index) => (
              <FadeInSection key={stage.id} delay={index * 200}>
                <div className="flex items-center justify-center">
                  <div 
                    className="flex items-center bg-white rounded-xl shadow-lg border-2 border-gray-200 p-6 transition-all duration-300 hover:shadow-xl hover:-translate-y-1"
                    style={{ width: `${Math.max(300, 700 - index * 80)}px` }}
                  >
                    <div className={cn(
                      'w-16 h-16 rounded-full flex items-center justify-center text-2xl text-white mr-6 bg-gradient-to-r',
                      stage.color
                    )}>
                      {stage.icon}
                    </div>
                    
                    <div className="flex-1">
                      <div className="flex items-center justify-between mb-2">
                        <h3 className="text-lg font-bold text-gray-900">
                          {stage.id}. {stage.title}
                        </h3>
                        <span className="text-sm font-semibold text-gray-600 bg-gray-100 px-3 py-1 rounded-full">
                          {stage.percentage}% of leads
                        </span>
                      </div>
                      <p className="text-gray-600 mb-3">{stage.description}</p>
                      <div className="space-y-1">
                        {stage.automation.slice(0, 2).map((item, idx) => (
                          <div key={idx} className="flex items-center text-sm text-gray-700">
                            <span className="text-green-500 mr-2">✓</span>
                            {item}
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              </FadeInSection>
            ))}
          </div>
        </div>
      </div>

      {/* Mobile/Tablet Funnel View */}
      <div className="lg:hidden space-y-6">
        {funnelStages.map((stage, index) => (
          <FadeInSection key={stage.id} delay={index * 150}>
            <div className="bg-white rounded-xl shadow-lg border border-gray-200 p-6">
              <div className="flex items-start space-x-4">
                <div className={cn(
                  'w-12 h-12 rounded-full flex items-center justify-center text-xl text-white bg-gradient-to-r flex-shrink-0',
                  stage.color
                )}>
                  {stage.icon}
                </div>
                
                <div className="flex-1">
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="text-lg font-bold text-gray-900">
                      {stage.id}. {stage.title}
                    </h3>
                    <span className="text-xs font-semibold text-gray-600 bg-gray-100 px-2 py-1 rounded-full">
                      {stage.percentage}%
                    </span>
                  </div>
                  <p className="text-gray-600 mb-3 text-sm">{stage.description}</p>
                  
                  <div className="space-y-2">
                    <h4 className="text-sm font-semibold text-gray-800">Automation Features:</h4>
                    {stage.automation.map((item, idx) => (
                      <div key={idx} className="flex items-start text-sm text-gray-700">
                        <span className="text-green-500 mr-2 mt-0.5">✓</span>
                        <span>{item}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </FadeInSection>
        ))}
      </div>

      {/* Funnel Benefits */}
      <FadeInSection delay={1200}>
        <div className="bg-gradient-to-br from-orange-50 to-red-50 rounded-2xl p-8 border border-orange-200">
          <div className="text-center mb-8">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              Why This Funnel Works for Real Estate
            </h3>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Our specialized funnel is designed specifically for real estate sales cycles and client behavior
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center text-2xl text-orange-600 mx-auto mb-4">
                ⏱️
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">Long Sales Cycles</h4>
              <p className="text-gray-600 text-sm">
                Designed for 30-90 day sales cycles typical in real estate
              </p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center text-2xl text-orange-600 mx-auto mb-4">
                🎯
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">Multiple Touchpoints</h4>
              <p className="text-gray-600 text-sm">
                Automated follow-ups maintain engagement throughout the process
              </p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center text-2xl text-orange-600 mx-auto mb-4">
                📊
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">Data-Driven Insights</h4>
              <p className="text-gray-600 text-sm">
                Track conversion rates and optimize each stage for better results
              </p>
            </div>
          </div>
        </div>
      </FadeInSection>
    </div>
  );
};

export default RealEstateFunnel;
