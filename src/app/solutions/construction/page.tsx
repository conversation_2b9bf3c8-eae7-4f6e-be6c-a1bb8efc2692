import React from 'react';
import { Metadata } from 'next';
import Button from '@/components/Button';
import Card from '@/components/Card';
import ContactForm from '@/components/ContactForm';
import FadeInSection from '@/components/FadeInSection';
import ConstructionResults from '@/components/ConstructionResults';
import ConstructionFunnel from '@/components/ConstructionFunnel';
import ConstructionTrust from '@/components/ConstructionTrust';
import AnimatedCTA from '@/components/AnimatedCTA';
import SolutionsContactForm from '@/components/SolutionsContactForm';

export const metadata: Metadata = {
  title: 'Kommo CRM for Construction Companies | Project & Client Management | Setmee',
  description: 'Transform your construction business with Kommo CRM. Automate project management, client communication, and lead tracking. Increase project efficiency by 40% and reduce admin tasks by 55%.',
  keywords: 'construction CRM, contractor CRM, project management CRM, Kommo CRM construction, building company CRM, construction automation',
  openGraph: {
    title: 'Kommo CRM for Construction Companies | Setmee',
    description: 'Automate your construction business with Kommo CRM. Proven to increase project efficiency by 40% and reduce administrative tasks by 55%.',
    type: 'website',
  },
};

export default function ConstructionPage() {
  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="relative py-20 lg:py-32 bg-gradient-to-br from-yellow-600 via-orange-600 to-orange-700 overflow-hidden">
        <div className="absolute inset-0 bg-black bg-opacity-20"></div>
        
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <FadeInSection>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <div>
                <div className="inline-block px-4 py-2 mb-6 bg-yellow-500 bg-opacity-20 text-yellow-100 rounded-full text-sm font-semibold">
                  🏗️ Construction CRM Solution
                </div>
                <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-6">
                  Kommo CRM for{' '}
                  <span className="bg-gradient-to-r from-yellow-300 to-orange-300 bg-clip-text text-transparent">
                    Construction Companies
                  </span>
                </h1>
                <p className="text-xl text-orange-100 mb-8 leading-relaxed">
                  Automate project management, client communication, and lead tracking. Increase project efficiency by 40% and reduce administrative tasks by 55%. Ready CRM solution for construction professionals.
                </p>

                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-8">
                  <div className="flex items-center">
                    <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center mr-3">
                      <span className="text-white text-sm">✓</span>
                    </div>
                    <span className="text-orange-100">+40% project efficiency</span>
                  </div>
                  <div className="flex items-center">
                    <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center mr-3">
                      <span className="text-white text-sm">✓</span>
                    </div>
                    <span className="text-orange-100">55% less admin tasks</span>
                  </div>
                  <div className="flex items-center">
                    <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center mr-3">
                      <span className="text-white text-sm">✓</span>
                    </div>
                    <span className="text-orange-100">Automated project tracking</span>
                  </div>
                  <div className="flex items-center">
                    <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center mr-3">
                      <span className="text-white text-sm">✓</span>
                    </div>
                    <span className="text-orange-100">Client communication automation</span>
                  </div>
                </div>

                <div className="flex flex-col sm:flex-row gap-4">
                  <AnimatedCTA
                    variant="secondary"
                    size="lg"
                    className="shadow-xl px-8 py-4 bg-white text-orange-600 hover:bg-orange-50 hover:text-orange-700 hover:shadow-2xl transition-all duration-300 border-0"
                    shakeDelay={10000}
                    href="#contact"
                  >
                    Get Free Demo
                  </AnimatedCTA>
                  <AnimatedCTA
                    variant="outline"
                    size="lg"
                    className="!bg-transparent !border-2 !border-white !text-white font-bold !px-8 !py-4 !transition-all !duration-300 focus:!outline-none focus:!ring-2 focus:!ring-white hover:!bg-white hover:!text-orange-600 hover:!border-orange-600 !rounded-lg !text-lg"
                    shakeDelay={15000}
                    disableCustomStyle={true}
                    href="/roi-calculator"
                  >
                    Calculate ROI
                  </AnimatedCTA>
                </div>
              </div>

              <div className="relative">
                <FadeInSection delay={300}>
                  <div className="bg-white bg-opacity-10 backdrop-blur-sm rounded-2xl p-8 border border-white border-opacity-20">
                    <div className="flex items-center mb-6">
                      <div className="w-12 h-12 bg-orange-500 rounded-lg flex items-center justify-center mr-4">
                        <span className="text-2xl">📊</span>
                      </div>
                      <div>
                        <h3 className="text-xl font-semibold text-white">Construction Dashboard</h3>
                        <p className="text-orange-200">Project pipeline, client management, and progress tracking in one place</p>
                      </div>
                    </div>
                    <div className="space-y-3">
                      <div className="flex justify-between items-center py-2 px-3 bg-white bg-opacity-10 rounded-lg">
                        <span className="text-orange-100">Live Updates</span>
                        <span className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></span>
                      </div>
                      <div className="flex justify-between items-center py-2 px-3 bg-white bg-opacity-10 rounded-lg">
                        <span className="text-orange-100">Real-time Analytics</span>
                        <span className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></span>
                      </div>
                    </div>
                  </div>
                </FadeInSection>
              </div>
            </div>
          </FadeInSection>
        </div>
      </section>

      {/* Problems Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <FadeInSection className="text-center mb-16">
            <div className="inline-block px-4 py-2 mb-6 bg-orange-100 text-orange-600 rounded-full text-sm font-semibold">
              🎯 Common Challenges
            </div>
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              What Construction Problems Does Kommo CRM Solve?
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Address the most common pain points in construction project management and client communication
            </p>
          </FadeInSection>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <FadeInSection delay={200}>
              <Card variant="outlined" className="text-center p-6 h-full hover:shadow-lg transition-shadow duration-300 border-red-200">
                <div className="text-4xl mb-4">📋</div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Manual Project Tracking</h3>
                <p className="text-gray-600 text-sm">Managing project progress across spreadsheets and paper forms</p>
                <div className="mt-4 text-green-600 text-sm font-medium">
                  → Automated project pipeline
                </div>
              </Card>
            </FadeInSection>

            <FadeInSection delay={350}>
              <Card variant="outlined" className="text-center p-6 h-full hover:shadow-lg transition-shadow duration-300 border-red-200">
                <div className="text-4xl mb-4">📞</div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Lost Client Communication</h3>
                <p className="text-gray-600 text-sm">Missing follow-ups and inconsistent project updates</p>
                <div className="mt-4 text-green-600 text-sm font-medium">
                  → Automated client communication
                </div>
              </Card>
            </FadeInSection>

            <FadeInSection delay={500}>
              <Card variant="outlined" className="text-center p-6 h-full hover:shadow-lg transition-shadow duration-300 border-red-200">
                <div className="text-4xl mb-4">⏰</div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Scheduling Conflicts</h3>
                <p className="text-gray-600 text-sm">Coordinating site visits, inspections, and meetings manually</p>
                <div className="mt-4 text-green-600 text-sm font-medium">
                  → Automated scheduling system
                </div>
              </Card>
            </FadeInSection>

            <FadeInSection delay={650}>
              <Card variant="outlined" className="text-center p-6 h-full hover:shadow-lg transition-shadow duration-300 border-red-200">
                <div className="text-4xl mb-4">📊</div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">No Project Insights</h3>
                <p className="text-gray-600 text-sm">Lack of data on project profitability and team performance</p>
                <div className="mt-4 text-green-600 text-sm font-medium">
                  → Real-time analytics dashboard
                </div>
              </Card>
            </FadeInSection>
          </div>
        </div>
      </section>

      {/* Funnel Section */}
      <ConstructionFunnel />

      {/* Results Section */}
      <ConstructionResults />

      {/* Automation Scenarios */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <FadeInSection className="text-center mb-16">
            <div className="inline-block px-4 py-2 mb-6 bg-orange-100 text-orange-600 rounded-full text-sm font-semibold">
              🤖 Automation Scenarios
            </div>
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Ready Construction Automation Solutions
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Choose the automation level that fits your construction company needs and technical requirements
            </p>
          </FadeInSection>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <FadeInSection delay={200}>
              <Card className="p-8 h-full border-2 border-orange-200 hover:border-orange-300 transition-colors duration-300">
                <div className="flex items-center mb-6">
                  <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mr-4">
                    <span className="text-2xl">⚡</span>
                  </div>
                  <h3 className="text-2xl font-bold text-gray-900">Basic CRM Automation</h3>
                </div>
                <ul className="space-y-4 mb-6">
                  <li className="flex items-start">
                    <span className="text-green-500 mr-3 mt-1">✓</span>
                    <span><strong>Project pipeline management:</strong> Automated status updates and milestone tracking</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-green-500 mr-3 mt-1">✓</span>
                    <span><strong>Client communication:</strong> Automated follow-up sequences and project updates</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-green-500 mr-3 mt-1">✓</span>
                    <span><strong>Appointment reminders:</strong> Automatic notifications for site visits and meetings</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-green-500 mr-3 mt-1">✓</span>
                    <span><strong>Basic reporting:</strong> Project progress tracking and team performance metrics</span>
                  </li>
                </ul>
                <div className="mt-6 p-4 bg-orange-50 rounded-lg">
                  <p className="text-orange-800 text-sm font-medium">Perfect for: Small to medium construction companies getting started with automation</p>
                </div>
              </Card>
            </FadeInSection>

            <FadeInSection delay={400}>
              <Card className="p-8 h-full border-2 border-purple-200 hover:border-purple-300 transition-colors duration-300">
                <div className="flex items-center mb-6">
                  <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mr-4">
                    <span className="text-2xl">🚀</span>
                  </div>
                  <h3 className="text-2xl font-bold text-gray-900">Advanced with Make.com</h3>
                </div>
                <ul className="space-y-4 mb-6">
                  <li className="flex items-start">
                    <span className="text-green-500 mr-3 mt-1">✓</span>
                    <span><strong>Multi-platform integration:</strong> Connect with QuickBooks, project management tools, and scheduling systems automatically</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-green-500 mr-3 mt-1">✓</span>
                    <span><strong>Smart lead qualification:</strong> Automated project assessment and budget qualification</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-green-500 mr-3 mt-1">✓</span>
                    <span><strong>Document automation:</strong> Automatic contract generation, invoicing, and progress reports</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-green-500 mr-3 mt-1">✓</span>
                    <span><strong>Advanced analytics:</strong> ROI tracking, project profitability analysis, and performance insights</span>
                  </li>
                </ul>
                <div className="mt-6 p-4 bg-purple-50 rounded-lg">
                  <p className="text-purple-800 text-sm font-medium">Perfect for: Established construction companies wanting maximum automation and efficiency</p>
                </div>
              </Card>
            </FadeInSection>
          </div>
        </div>
      </section>

      {/* Trust Section */}
      <ConstructionTrust />

      {/* Final CTA */}
      <section className="py-20 bg-gradient-to-br from-orange-600 via-orange-700 to-red-700 relative overflow-hidden">
        <div className="absolute inset-0 bg-black bg-opacity-20"></div>
        
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <FadeInSection>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <div>
                <div className="inline-block px-4 py-2 mb-6 bg-orange-500 bg-opacity-20 text-orange-100 rounded-full text-sm font-semibold">
                  🚀 Get Started Today
                </div>
                <h2 className="text-3xl md:text-4xl font-bold mb-6">
                  Ready to Transform Your Construction Business?
                </h2>
                <p className="text-xl text-orange-100 mb-8">
                  Join hundreds of construction companies that have automated their processes with Kommo CRM. Get a personalized demo and see results in the first week.
                </p>
                
                <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 mb-8">
                  <div className="flex items-center">
                    <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center mr-3">
                      <span className="text-white text-sm">✓</span>
                    </div>
                    <span className="text-orange-100">40% more project efficiency</span>
                  </div>
                  <div className="flex items-center">
                    <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center mr-3">
                      <span className="text-white text-sm">✓</span>
                    </div>
                    <span className="text-orange-100">55% reduction in admin tasks</span>
                  </div>
                  <div className="flex items-center">
                    <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center mr-3">
                      <span className="text-white text-sm">✓</span>
                    </div>
                    <span className="text-orange-100">3x faster project setup</span>
                  </div>
                </div>
              </div>

              <div id="contact">
                <SolutionsContactForm />
              </div>
            </div>
          </FadeInSection>
        </div>
      </section>
    </div>
  );
}
