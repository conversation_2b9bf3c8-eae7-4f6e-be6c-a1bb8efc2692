import React from 'react';
import { Metadata } from 'next';
import Button from '@/components/Button';
import Card from '@/components/Card';
import ContactForm from '@/components/ContactForm';
import FadeInSection from '@/components/FadeInSection';
import SaasResults from '@/components/SaasResults';
import SaasFunnel from '@/components/SaasFunnel';
import SaasTrust from '@/components/SaasTrust';
import AnimatedCTA from '@/components/AnimatedCTA';
import SolutionsContactForm from '@/components/SolutionsContactForm';

export const metadata: Metadata = {
  title: 'Kommo CRM for SaaS & Tech Companies | Setmee',
  description: 'Kommo CRM implementation for SaaS and tech companies. Automate sales processes, manage customer lifecycle, and integrate with your tech stack. Expert CRM setup and optimization.',
  keywords: 'SaaS CRM implementation, tech company CRM, Kommo CRM setup, sales automation SaaS, customer lifecycle management, CRM integration',
  openGraph: {
    title: 'Kommo CRM for SaaS & Tech Companies | Setmee',
    description: 'Professional Kommo CRM implementation for SaaS and tech companies. Streamline sales processes and customer management.',
    type: 'website',
  },
};

export default function SaasPage() {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative py-20 lg:py-32 bg-gradient-to-br from-cyan-600 via-blue-600 to-indigo-700 text-white overflow-hidden">
        <div className="absolute inset-0 bg-black opacity-20"></div>
        
        {/* Animated background elements */}
        <div className="absolute top-20 left-10 w-64 h-64 bg-white opacity-5 rounded-full animate-pulse"></div>
        <div className="absolute bottom-20 right-10 w-48 h-48 bg-white opacity-10 rounded-full animate-bounce"></div>
        <div className="absolute top-1/2 left-1/3 w-32 h-32 bg-white opacity-5 rounded-full"></div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            {/* Left Content */}
            <FadeInSection className="text-white">
              <div className="inline-block px-4 py-2 mb-6 bg-cyan-500 bg-opacity-20 text-cyan-100 rounded-full text-sm font-semibold">
                🚀 SaaS & Tech CRM Solution
              </div>
              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 leading-tight">
                Kommo CRM for <span className="text-cyan-200">SaaS & Tech Companies</span>
              </h1>
              <p className="text-xl md:text-2xl text-cyan-100 mb-8 leading-relaxed">
                Professional Kommo CRM implementation and automation for SaaS and tech companies.
                Streamline sales processes, manage customer lifecycle, and integrate with your existing tech stack.
                Expert setup and ongoing optimization.
              </p>

              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-8">
                <div className="flex items-center">
                  <div className="w-8 h-8 bg-cyan-500 rounded-full flex items-center justify-center mr-3">
                    <span className="text-white text-sm">✓</span>
                  </div>
                  <span className="text-cyan-100">Product-led growth automation</span>
                </div>
                <div className="flex items-center">
                  <div className="w-8 h-8 bg-cyan-500 rounded-full flex items-center justify-center mr-3">
                    <span className="text-white text-sm">✓</span>
                  </div>
                  <span className="text-cyan-100">Usage-based lead scoring</span>
                </div>
                <div className="flex items-center">
                  <div className="w-8 h-8 bg-cyan-500 rounded-full flex items-center justify-center mr-3">
                    <span className="text-white text-sm">✓</span>
                  </div>
                  <span className="text-cyan-100">Developer-friendly APIs</span>
                </div>
                <div className="flex items-center">
                  <div className="w-8 h-8 bg-cyan-500 rounded-full flex items-center justify-center mr-3">
                    <span className="text-white text-sm">✓</span>
                  </div>
                  <span className="text-cyan-100">SaaS metrics & analytics</span>
                </div>
              </div>

              <div className="flex flex-col sm:flex-row gap-4 justify-start items-center mb-8">
                <AnimatedCTA
                  variant="secondary"
                  size="lg"
                  className="shadow-xl px-8 py-4 bg-white text-gray-800 hover:bg-gray-100 hover:text-gray-900 hover:shadow-2xl transition-all duration-300 border-0"
                  shakeDelay={10000}
                >
                  Start Free Trial
                </AnimatedCTA>
                <AnimatedCTA
                  variant="outline"
                  size="lg"
                  className="bg-transparent border-2 border-white text-white font-bold px-8 py-4 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-white hover:bg-white hover:text-cyan-700 hover:border-cyan-700"
                  shakeDelay={15000}
                  disableCustomStyle={true}
                >
                  See PLG Demo
                </AnimatedCTA>
              </div>
            </FadeInSection>

            {/* Right Content - SaaS Dashboard Preview */}
            <FadeInSection delay={300} className="hidden lg:block">
              <div className="relative">
                <div className="bg-white bg-opacity-10 backdrop-blur-sm rounded-2xl p-8 border border-white border-opacity-20">
                  <div className="text-center text-white">
                    <div className="text-6xl mb-4">🚀</div>
                    <h3 className="text-xl font-semibold mb-2">SaaS Growth Dashboard</h3>
                    <p className="text-cyan-100 text-sm">
                      Product-led growth metrics, user journey tracking, and automated expansion
                    </p>
                  </div>
                </div>

                {/* Floating elements */}
                <div className="absolute -top-4 -right-4 bg-cyan-500 text-white px-3 py-1 rounded-full text-sm font-semibold">
                  PLG Ready
                </div>
                <div className="absolute -bottom-4 -left-4 bg-blue-500 text-white px-3 py-1 rounded-full text-sm font-semibold">
                  API First
                </div>
              </div>
            </FadeInSection>
          </div>
        </div>
      </section>

      {/* Problems Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <FadeInSection>
            <div className="text-center mb-16">
              <div className="inline-block px-4 py-2 mb-6 bg-red-100 text-red-700 rounded-full text-sm font-semibold">
                ⚠️ CRM Challenges in SaaS & Tech
              </div>
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                CRM Challenges in SaaS & Tech Companies
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                SaaS and tech companies need specialized CRM setup to manage complex sales cycles and customer relationships
              </p>
            </div>
          </FadeInSection>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <FadeInSection delay={200}>
              <Card variant="outlined" className="text-center p-6 h-full hover:shadow-lg transition-shadow duration-300 border-red-200">
                <div className="text-4xl mb-4">📊</div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Complex Sales Cycles</h3>
                <p className="text-gray-600 text-sm">Multiple decision makers and long evaluation periods</p>
                <div className="mt-4 text-cyan-600 text-sm font-medium">
                  → CRM automation
                </div>
              </Card>
            </FadeInSection>

            <FadeInSection delay={350}>
              <Card variant="outlined" className="text-center p-6 h-full hover:shadow-lg transition-shadow duration-300 border-red-200">
                <div className="text-4xl mb-4">🔗</div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Integration Challenges</h3>
                <p className="text-gray-600 text-sm">Need to connect CRM with multiple tech tools and APIs</p>
                <div className="mt-4 text-cyan-600 text-sm font-medium">
                  → Make.com integrations
                </div>
              </Card>
            </FadeInSection>

            <FadeInSection delay={500}>
              <Card variant="outlined" className="text-center p-6 h-full hover:shadow-lg transition-shadow duration-300 border-red-200">
                <div className="text-4xl mb-4">📈</div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Customer Lifecycle Management</h3>
                <p className="text-gray-600 text-sm">Tracking customers from trial to expansion and renewal</p>
                <div className="mt-4 text-cyan-600 text-sm font-medium">
                  → Lifecycle automation
                </div>
              </Card>
            </FadeInSection>

            <FadeInSection delay={650}>
              <Card variant="outlined" className="text-center p-6 h-full hover:shadow-lg transition-shadow duration-300 border-red-200">
                <div className="text-4xl mb-4">⚡</div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Manual Sales Processes</h3>
                <p className="text-gray-600 text-sm">Time-consuming manual tasks reducing team efficiency</p>
                <div className="mt-4 text-cyan-600 text-sm font-medium">
                  → Process automation
                </div>
              </Card>
            </FadeInSection>
          </div>
        </div>
      </section>

      {/* SaaS Sales Funnel */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <SaasFunnel />
        </div>
      </section>

      {/* Automation Scenarios */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <FadeInSection>
            <div className="text-center mb-16">
              <div className="inline-block px-4 py-2 mb-6 bg-cyan-100 text-cyan-700 rounded-full text-sm font-semibold">
                🤖 Product-Led Growth Automation
              </div>
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                SaaS & Tech Company Automation with Kommo CRM
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Choose the automation level that matches your SaaS growth stage and technical requirements
              </p>
            </div>
          </FadeInSection>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <FadeInSection delay={200}>
              <Card variant="elevated" className="p-8 h-full border-l-4 border-l-cyan-500">
                <div className="flex items-center mb-6">
                  <div className="w-12 h-12 bg-cyan-100 rounded-full flex items-center justify-center text-cyan-600 text-xl mr-4">
                    ⚡
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900">Standard Automation</h3>
                </div>
                <ul className="space-y-4 text-gray-700">
                  <li className="flex items-start">
                    <span className="text-cyan-500 mr-3 mt-1">✓</span>
                    <span>
                      <strong>Trial tracking:</strong> Monitor user activation and feature adoption
                    </span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-cyan-500 mr-3 mt-1">✓</span>
                    <span>
                      <strong>Usage scoring:</strong> Automatically score leads based on product engagement
                    </span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-cyan-500 mr-3 mt-1">✓</span>
                    <span>
                      <strong>Conversion flows:</strong> Automated sequences for trial-to-paid conversion
                    </span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-cyan-500 mr-3 mt-1">✓</span>
                    <span>
                      <strong>Churn prevention:</strong> Early warning system for at-risk customers
                    </span>
                  </li>
                </ul>
                <div className="mt-6 p-4 bg-cyan-50 rounded-lg">
                  <p className="text-cyan-700 text-sm font-medium">
                    Perfect for growing SaaS companies 
                  </p>
                </div>
              </Card>
            </FadeInSection>

            <FadeInSection delay={400}>
              <Card variant="elevated" className="p-8 h-full border-l-4 border-l-blue-500">
                <div className="flex items-center mb-6">
                  <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 text-xl mr-4">
                    🚀
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900">Enterprise with Make.com</h3>
                </div>
                <ul className="space-y-4 text-gray-700">
                  <li className="flex items-start">
                    <span className="text-cyan-500 mr-3 mt-1">✓</span>
                    <span>
                      <strong>Advanced analytics:</strong> Cohort analysis, LTV prediction, and custom dashboards
                    </span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-cyan-500 mr-3 mt-1">✓</span>
                    <span>
                      <strong>API integrations:</strong> Connect with your product, billing, and support systems
                    </span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-cyan-500 mr-3 mt-1">✓</span>
                    <span>
                      <strong>Multi-product tracking:</strong> Manage complex product suites and cross-selling
                    </span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-cyan-500 mr-3 mt-1">✓</span>
                    <span>
                      <strong>Enterprise sales:</strong> Account-based marketing and complex deal management
                    </span>
                  </li>
                </ul>
                <div className="mt-6 p-4 bg-blue-50 rounded-lg">
                  <p className="text-blue-700 text-sm font-medium">
                    Ideal for enterprise SaaS companies 
                  </p>
                </div>
              </Card>
            </FadeInSection>
          </div>
        </div>
      </section>

      {/* Results & Testimonials */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <SaasResults />
        </div>
      </section>

      {/* Trust Elements */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <SaasTrust />
        </div>
      </section>

      {/* Final CTA */}
      <section className="py-20 bg-gradient-to-br from-cyan-600 via-blue-600 to-indigo-700 text-white relative overflow-hidden">
        <div className="absolute inset-0 bg-black opacity-10"></div>

        {/* Decorative elements */}
        <div className="absolute top-10 right-10 w-32 h-32 bg-white opacity-5 rounded-full"></div>
        <div className="absolute bottom-10 left-10 w-24 h-24 bg-white opacity-10 rounded-full"></div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <FadeInSection>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <div>
                <div className="inline-block px-4 py-2 mb-6 bg-cyan-500 bg-opacity-20 text-cyan-100 rounded-full text-sm font-semibold">
                  🚀 Product-Led Growth Ready
                </div>
                <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6 leading-tight">
                  Ready to Scale Your <span className="text-cyan-200">SaaS Business</span>?
                </h2>
                <p className="text-xl md:text-2xl text-cyan-100 mb-8 leading-relaxed">
                  Get a personalized demo of Kommo CRM configured for your SaaS or tech company.
                  See how product-led growth automation can transform your customer journey.
                </p>

                <div className="space-y-4 mb-8">
                  <div className="flex items-center">
                    <span className="text-cyan-300 mr-3">✓</span>
                    <span>Custom automation workflows for your product</span>
                  </div>
                  <div className="flex items-center">
                    <span className="text-cyan-300 mr-3">✓</span>
                    <span>Developer-friendly implementation with API access</span>
                  </div>
                </div>

              </div>
              <div>
                <SolutionsContactForm className="bg-white text-gray-900 shadow-2xl" />
              </div>
            </div>
          </FadeInSection>
        </div>
      </section>
    </div>
  );
}
