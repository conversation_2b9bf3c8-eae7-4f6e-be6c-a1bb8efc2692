import React from 'react';
import { Metadata } from 'next';
import Button from '@/components/Button';
import Card from '@/components/Card';
import ContactForm from '@/components/ContactForm';
import FadeInSection from '@/components/FadeInSection';
import B2BResults from '@/components/B2BResults';
import B2BFunnel from '@/components/B2BFunnel';
import B2BTrust from '@/components/B2BTrust';
import AnimatedCTA from '@/components/AnimatedCTA';
import SolutionsContactForm from '@/components/SolutionsContactForm';

export const metadata: Metadata = {
  title: 'Kommo CRM for B2B Companies | Setmee',
  description: 'Automate complex B2B sales cycles and business relationships. Increase lead quality by 180% and reduce sales cycle time by 65% with specialized CRM automation.',
  keywords: 'B2B CRM, business sales automation, enterprise CRM, B2B lead management, corporate sales CRM',
  openGraph: {
    title: 'Kommo CRM for B2B Companies | Setmee',
    description: 'Transform your B2B business with automated sales processes and streamlined customer relationship management.',
    type: 'website',
  },
};

export default function B2BPage() {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative py-20 lg:py-32 bg-gradient-to-br from-blue-600 via-indigo-600 to-purple-700 text-white overflow-hidden">
        <div className="absolute inset-0 bg-black opacity-20"></div>
        
        {/* Animated background elements */}
        <div className="absolute top-20 left-10 w-64 h-64 bg-white opacity-5 rounded-full animate-pulse"></div>
        <div className="absolute bottom-20 right-10 w-48 h-48 bg-white opacity-10 rounded-full animate-bounce"></div>
        <div className="absolute top-1/2 left-1/3 w-32 h-32 bg-white opacity-5 rounded-full"></div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            {/* Left Content */}
            <FadeInSection className="text-white">
              <div className="inline-block px-4 py-2 mb-6 bg-blue-500 bg-opacity-20 text-blue-100 rounded-full text-sm font-semibold">
                🏢 B2B CRM Solution
              </div>
              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 leading-tight">
                Kommo CRM for <span className="text-blue-200">B2B Companies</span>
              </h1>
              <p className="text-xl md:text-2xl text-blue-100 mb-8 leading-relaxed">
                Automate complex B2B sales cycles and business relationships.
                Increase lead quality by 180% and reduce sales cycle time by 65%.
                Enterprise-ready solution for professional services and corporate sales.
              </p>

              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-8">
                <div className="flex items-center">
                  <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center mr-3">
                    <span className="text-white text-sm">✓</span>
                  </div>
                  <span className="text-blue-100">Multi-stakeholder management</span>
                </div>
                <div className="flex items-center">
                  <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center mr-3">
                    <span className="text-white text-sm">✓</span>
                  </div>
                  <span className="text-blue-100">Custom quote automation</span>
                </div>
                <div className="flex items-center">
                  <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center mr-3">
                    <span className="text-white text-sm">✓</span>
                  </div>
                  <span className="text-blue-100">Enterprise integrations</span>
                </div>
                <div className="flex items-center">
                  <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center mr-3">
                    <span className="text-white text-sm">✓</span>
                  </div>
                  <span className="text-blue-100">Advanced analytics & reporting</span>
                </div>
              </div>

              <div className="flex flex-col sm:flex-row gap-4 justify-start items-center mb-8">
                <AnimatedCTA
                  variant="secondary"
                  size="lg"
                  className="shadow-xl px-8 py-4 bg-white text-gray-800 hover:bg-gray-100 hover:text-gray-900 hover:shadow-2xl transition-all duration-300 border-0"
                  shakeDelay={10000}
                >
                  Get Enterprise Demo
                </AnimatedCTA>
                <AnimatedCTA
                  variant="outline"
                  size="lg"
                  className="bg-transparent border-2 border-white text-white font-bold px-8 py-4 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-white hover:bg-white hover:text-blue-700 hover:border-blue-700"
                  shakeDelay={15000}
                  disableCustomStyle={true}
                >
                  Download B2B Guide
                </AnimatedCTA>
              </div>
            </FadeInSection>

            {/* Right Content - B2B Dashboard Preview */}
            <FadeInSection delay={300} className="hidden lg:block">
              <div className="relative">
                <div className="bg-white bg-opacity-10 backdrop-blur-sm rounded-2xl p-8 border border-white border-opacity-20">
                  <div className="text-center text-white">
                    <div className="text-6xl mb-4">🏢</div>
                    <h3 className="text-xl font-semibold mb-2">B2B Sales Dashboard</h3>
                    <p className="text-blue-100 text-sm">
                      Complex deal tracking, multi-stakeholder management, and enterprise analytics
                    </p>
                  </div>
                </div>

                {/* Floating elements */}
                <div className="absolute -top-4 -right-4 bg-blue-500 text-white px-3 py-1 rounded-full text-sm font-semibold">
                  Enterprise Ready
                </div>
                <div className="absolute -bottom-4 -left-4 bg-indigo-500 text-white px-3 py-1 rounded-full text-sm font-semibold">
                  SOC 2 Compliant
                </div>
              </div>
            </FadeInSection>
          </div>
        </div>
      </section>

      {/* Problems Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <FadeInSection>
            <div className="text-center mb-16">
              <div className="inline-block px-4 py-2 mb-6 bg-red-100 text-red-700 rounded-full text-sm font-semibold">
                ⚠️ B2B Sales Challenges
              </div>
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                Complex Challenges in B2B Sales
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                B2B companies face unique sales challenges that traditional CRM solutions can't handle effectively
              </p>
            </div>
          </FadeInSection>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <FadeInSection delay={200}>
              <Card variant="outlined" className="text-center p-6 h-full hover:shadow-lg transition-shadow duration-300 border-red-200">
                <div className="text-4xl mb-4">🕐</div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Long Sales Cycles</h3>
                <p className="text-gray-600 text-sm">3-12 month decision processes with multiple stakeholders</p>
                <div className="mt-4 text-blue-600 text-sm font-medium">
                  → Automated nurturing sequences
                </div>
              </Card>
            </FadeInSection>

            <FadeInSection delay={350}>
              <Card variant="outlined" className="text-center p-6 h-full hover:shadow-lg transition-shadow duration-300 border-red-200">
                <div className="text-4xl mb-4">👥</div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Multiple Decision Makers</h3>
                <p className="text-gray-600 text-sm">Complex approval chains and stakeholder management</p>
                <div className="mt-4 text-blue-600 text-sm font-medium">
                  → Multi-contact tracking
                </div>
              </Card>
            </FadeInSection>

            <FadeInSection delay={500}>
              <Card variant="outlined" className="text-center p-6 h-full hover:shadow-lg transition-shadow duration-300 border-red-200">
                <div className="text-4xl mb-4">💰</div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Custom Pricing & Quotes</h3>
                <p className="text-gray-600 text-sm">Complex pricing models and custom quote generation</p>
                <div className="mt-4 text-blue-600 text-sm font-medium">
                  → Quote automation in CRM
                </div>
              </Card>
            </FadeInSection>

            <FadeInSection delay={650}>
              <Card variant="outlined" className="text-center p-6 h-full hover:shadow-lg transition-shadow duration-300 border-red-200">
                <div className="text-4xl mb-4">🔗</div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">System Integration</h3>
                <p className="text-gray-600 text-sm">Need to connect with ERP, inventory, and accounting systems</p>
                <div className="mt-4 text-blue-600 text-sm font-medium">
                  → CRM integrations via Make.com
                </div>
              </Card>
            </FadeInSection>
          </div>
        </div>
      </section>

      {/* B2B Sales Funnel */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <B2BFunnel />
        </div>
      </section>

      {/* Automation Scenarios */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <FadeInSection>
            <div className="text-center mb-16">
              <div className="inline-block px-4 py-2 mb-6 bg-blue-100 text-blue-700 rounded-full text-sm font-semibold">
                🤖 Enterprise Automation
              </div>
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                B2B Sales Automation with Kommo CRM
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Choose the automation level that matches your B2B complexity and enterprise requirements
              </p>
            </div>
          </FadeInSection>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <FadeInSection delay={200}>
              <Card variant="elevated" className="p-8 h-full border-l-4 border-l-blue-500">
                <div className="flex items-center mb-6">
                  <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 text-xl mr-4">
                    ⚡
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900">Standard B2B Automation</h3>
                </div>
                <ul className="space-y-4 text-gray-700">
                  <li className="flex items-start">
                    <span className="text-blue-500 mr-3 mt-1">✓</span>
                    <span><strong>Lead qualification:</strong> Automated scoring based on company size and industry</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-blue-500 mr-3 mt-1">✓</span>
                    <span><strong>Multi-contact tracking:</strong> Manage multiple stakeholders in one deal</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-blue-500 mr-3 mt-1">✓</span>
                    <span><strong>Quote automation:</strong> Generate custom proposals and pricing</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-blue-500 mr-3 mt-1">✓</span>
                    <span><strong>Approval workflows:</strong> Route deals through proper authorization chains</span>
                  </li>
                </ul>
                <div className="mt-6 p-4 bg-blue-50 rounded-lg">
                  <p className="text-blue-700 text-sm font-medium">
                    Perfect for mid-market B2B companies
                  </p>
                </div>
              </Card>
            </FadeInSection>

            <FadeInSection delay={400}>
              <Card variant="elevated" className="p-8 h-full border-l-4 border-l-indigo-500">
                <div className="flex items-center mb-6">
                  <div className="w-12 h-12 bg-indigo-100 rounded-full flex items-center justify-center text-indigo-600 text-xl mr-4">
                    🚀
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900">Enterprise with Make.com</h3>
                </div>
                <ul className="space-y-4 text-gray-700">
                  <li className="flex items-start">
                    <span className="text-blue-500 mr-3 mt-1">✓</span>
                    <span><strong>ERP integration:</strong> Connect with SAP, Oracle, NetSuite, and other systems</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-blue-500 mr-3 mt-1">✓</span>
                    <span><strong>Advanced analytics:</strong> Custom dashboards and enterprise reporting</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-blue-500 mr-3 mt-1">✓</span>
                    <span><strong>Inventory sync:</strong> Real-time product availability and pricing updates</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-blue-500 mr-3 mt-1">✓</span>
                    <span><strong>Custom workflows:</strong> Industry-specific automation and compliance</span>
                  </li>
                </ul>
                <div className="mt-6 p-4 bg-indigo-50 rounded-lg">
                  <p className="text-indigo-700 text-sm font-medium">
                    Ideal for enterprise B2B companies 
                  </p>
                </div>
              </Card>
            </FadeInSection>
          </div>
        </div>
      </section>

      {/* Results & Testimonials */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <B2BResults />
        </div>
      </section>

      {/* Trust Elements */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <B2BTrust />
        </div>
      </section>

      {/* Final CTA */}
      <section className="py-20 bg-gradient-to-br from-blue-600 via-indigo-600 to-purple-700 text-white relative overflow-hidden">
        <div className="absolute inset-0 bg-black opacity-10"></div>

        {/* Decorative elements */}
        <div className="absolute top-10 right-10 w-32 h-32 bg-white opacity-5 rounded-full"></div>
        <div className="absolute bottom-10 left-10 w-24 h-24 bg-white opacity-10 rounded-full"></div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <FadeInSection>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <div>
                <div className="inline-block px-4 py-2 mb-6 bg-blue-500 bg-opacity-20 text-blue-100 rounded-full text-sm font-semibold">
                  🚀 Enterprise Ready
                </div>
                <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6 leading-tight">
                  Ready to Scale Your <span className="text-blue-200">B2B Sales</span>?
                </h2>
                <p className="text-xl md:text-2xl text-blue-100 mb-8 leading-relaxed">
                  Get a personalized demo of Kommo CRM configured for your specific B2B requirements.
                  See how enterprise automation can transform your complex sales processes.
                </p>

                <div className="space-y-4 mb-8">
                  <div className="flex items-center">
                    <span className="text-blue-300 mr-3">✓</span>
                    <span>Free enterprise consultation and needs assessment</span>
                  </div>
                  <div className="flex items-center">
                    <span className="text-blue-300 mr-3">✓</span>
                    <span>Custom automation workflows for your industry</span>
                  </div>
                  <div className="flex items-center">
                    <span className="text-blue-300 mr-3">✓</span>
                    <span>Enterprise implementation with dedicated support</span>
                  </div>
                </div>


              </div>
              <div>
                <SolutionsContactForm className="bg-white text-gray-900 shadow-2xl" />
              </div>
            </div>
          </FadeInSection>
        </div>
      </section>
    </div>
  );
}
