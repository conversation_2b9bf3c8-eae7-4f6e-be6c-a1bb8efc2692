import React from 'react';
import { Metadata } from 'next';
import Button from '@/components/Button';
import Card from '@/components/Card';
import ContactForm from '@/components/ContactForm';
import FadeInSection from '@/components/FadeInSection';
import DigitalMarketingResults from '@/components/DigitalMarketingResults';
import DigitalMarketingFunnel from '@/components/DigitalMarketingFunnel';
import DigitalMarketingTrust from '@/components/DigitalMarketingTrust';
import AnimatedCTA from '@/components/AnimatedCTA';
import SolutionsContactForm from '@/components/SolutionsContactForm';

export const metadata: Metadata = {
  title: 'Kommo CRM for Digital Marketing Agencies | Setmee',
  description: 'Automate client management, lead nurturing, and sales processes for digital marketing agencies. Increase efficiency by 60% and client retention by 40% with Kommo CRM.',
  keywords: 'digital marketing CRM, agency management, client management, lead automation, sales automation',
  openGraph: {
    title: 'Kommo CRM for Digital Marketing Agencies | Setmee',
    description: 'Transform your digital marketing agency with automated client management and sales processes.',
    type: 'website',
  },
};

export default function DigitalMarketingPage() {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative py-20 lg:py-32 bg-gradient-to-br from-blue-600 via-purple-600 to-pink-700 text-white overflow-hidden">
        <div className="absolute inset-0 bg-black opacity-20"></div>
        
        {/* Animated background elements */}
        <div className="absolute top-20 left-10 w-64 h-64 bg-white opacity-5 rounded-full animate-pulse"></div>
        <div className="absolute bottom-20 right-10 w-48 h-48 bg-white opacity-10 rounded-full animate-bounce"></div>
        <div className="absolute top-1/2 left-1/3 w-32 h-32 bg-white opacity-5 rounded-full"></div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            {/* Left Content */}
            <FadeInSection className="text-white">
              <div className="inline-block px-4 py-2 mb-6 bg-blue-500 bg-opacity-20 text-blue-100 rounded-full text-sm font-semibold">
                📱 Digital Marketing CRM Solution
              </div>
              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 leading-tight">
                Kommo CRM for <span className="text-blue-200">Digital Marketing Agencies</span>
              </h1>
              <p className="text-xl md:text-2xl text-blue-100 mb-8 leading-relaxed">
                Automate client management, lead nurturing, and sales processes.
                Increase team efficiency by 60% and client retention by 40%.
                Ready CRM solution for modern marketing agencies.
              </p>

              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-8">
                <div className="flex items-center">
                  <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center mr-3">
                    <span className="text-white text-sm">✓</span>
                  </div>
                  <span className="text-blue-100">Multi-channel lead management</span>
                </div>
                <div className="flex items-center">
                  <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center mr-3">
                    <span className="text-white text-sm">✓</span>
                  </div>
                  <span className="text-blue-100">Automated client reporting</span>
                </div>
                <div className="flex items-center">
                  <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center mr-3">
                    <span className="text-white text-sm">✓</span>
                  </div>
                  <span className="text-blue-100">Lead scoring & qualification</span>
                </div>
                <div className="flex items-center">
                  <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center mr-3">
                    <span className="text-white text-sm">✓</span>
                  </div>
                  <span className="text-blue-100">Sales team performance tracking</span>
                </div>
              </div>

              <div className="flex flex-col sm:flex-row gap-4 justify-start items-center mb-8">
                <AnimatedCTA
                  variant="secondary"
                  size="lg"
                  className="shadow-xl px-8 py-4 bg-white text-gray-800 hover:bg-gray-100 hover:text-gray-900 hover:shadow-2xl transition-all duration-300 border-0"
                  shakeDelay={10000}
                >
                  Get Demo Access
                </AnimatedCTA>
                <AnimatedCTA
                  variant="outline"
                  size="lg"
                  className="bg-transparent border-2 border-white text-white font-bold px-8 py-4 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-white hover:bg-white hover:text-blue-700 hover:border-blue-700"
                  shakeDelay={15000}
                  disableCustomStyle={true}
                >
                  Calculate ROI
                </AnimatedCTA>
              </div>
            </FadeInSection>

            {/* Right Content - Dashboard Preview */}
            <FadeInSection delay={300} className="hidden lg:block">
              <div className="relative">
                <div className="bg-white bg-opacity-10 backdrop-blur-sm rounded-2xl p-8 border border-white border-opacity-20">
                  <div className="text-center text-white">
                    <div className="text-6xl mb-4">📊</div>
                    <h3 className="text-xl font-semibold mb-2">CRM Dashboard</h3>
                    <p className="text-blue-100 text-sm">
                      Client pipeline, lead management, and sales analytics in one place
                    </p>
                  </div>
                </div>

                {/* Floating elements */}
                <div className="absolute -top-4 -right-4 bg-green-500 text-white px-3 py-1 rounded-full text-sm font-semibold">
                  Live Demo Available
                </div>
                <div className="absolute -bottom-4 -left-4 bg-purple-500 text-white px-3 py-1 rounded-full text-sm font-semibold">
                  Free Setup
                </div>
              </div>
            </FadeInSection>
          </div>
        </div>
      </section>

      {/* Problems Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <FadeInSection>
            <div className="text-center mb-16">
              <div className="inline-block px-4 py-2 mb-6 bg-red-100 text-red-700 rounded-full text-sm font-semibold">
                ⚠️ Common Agency Challenges
              </div>
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                Problems Digital Marketing Agencies Face Daily
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Without proper CRM automation, agencies struggle with client management,
                lead nurturing, and sales coordination
              </p>
            </div>
          </FadeInSection>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <FadeInSection delay={200}>
              <Card variant="outlined" className="text-center p-6 h-full hover:shadow-lg transition-shadow duration-300 border-red-200">
                <div className="text-4xl mb-4">📊</div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Manual Reporting</h3>
                <p className="text-gray-600 text-sm">Creating client reports manually takes hours each week</p>
                <div className="mt-4 text-green-600 text-sm font-medium">
                  → Automated report generation
                </div>
              </Card>
            </FadeInSection>

            <FadeInSection delay={350}>
              <Card variant="outlined" className="text-center p-6 h-full hover:shadow-lg transition-shadow duration-300 border-red-200">
                <div className="text-4xl mb-4">🎯</div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Lead Chaos</h3>
                <p className="text-gray-600 text-sm">Managing multiple lead sources without unified tracking and follow-up</p>
                <div className="mt-4 text-green-600 text-sm font-medium">
                  → Centralized lead management
                </div>
              </Card>
            </FadeInSection>

            <FadeInSection delay={500}>
              <Card variant="outlined" className="text-center p-6 h-full hover:shadow-lg transition-shadow duration-300 border-red-200">
                <div className="text-4xl mb-4">📞</div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Client Communication</h3>
                <p className="text-gray-600 text-sm">Inconsistent client updates and missed follow-ups hurt relationships</p>
                <div className="mt-4 text-green-600 text-sm font-medium">
                  → Automated client communication
                </div>
              </Card>
            </FadeInSection>

            <FadeInSection delay={650}>
              <Card variant="outlined" className="text-center p-6 h-full hover:shadow-lg transition-shadow duration-300 border-red-200">
                <div className="text-4xl mb-4">⚡</div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Team Inefficiency</h3>
                <p className="text-gray-600 text-sm">Team members waste time on administrative tasks instead of strategy</p>
                <div className="mt-4 text-green-600 text-sm font-medium">
                  → Workflow automation
                </div>
              </Card>
            </FadeInSection>
          </div>
        </div>
      </section>

      {/* Digital Marketing Sales Funnel */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <DigitalMarketingFunnel />
        </div>
      </section>

      {/* Automation Scenarios */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <FadeInSection>
            <div className="text-center mb-16">
              <div className="inline-block px-4 py-2 mb-6 bg-blue-100 text-blue-700 rounded-full text-sm font-semibold">
                🤖 Automation Scenarios
              </div>
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                Digital Marketing Automation with Kommo CRM
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Choose the automation level that fits your agency needs and technical requirements
              </p>
            </div>
          </FadeInSection>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <FadeInSection delay={200}>
              <Card variant="elevated" className="p-8 h-full border-l-4 border-l-blue-500">
                <div className="flex items-center mb-6">
                  <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 text-xl mr-4">
                    ⚡
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900">Basic Kommo Automation</h3>
                </div>
                <ul className="space-y-4 text-gray-700">
                  <li className="flex items-start">
                    <span className="text-green-500 mr-3 mt-1">✓</span>
                    <span><strong>Lead capture:</strong> Automatic lead collection from website forms and landing pages</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-green-500 mr-3 mt-1">✓</span>
                    <span><strong>Client communication:</strong> Automated email sequences and follow-up reminders</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-green-500 mr-3 mt-1">✓</span>
                    <span><strong>Task management:</strong> Automatic task assignment and follow-up tracking</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-green-500 mr-3 mt-1">✓</span>
                    <span><strong>Basic reporting:</strong> Weekly sales summaries and client updates</span>
                  </li>
                </ul>
                <div className="mt-6 p-4 bg-blue-50 rounded-lg">
                  <p className="text-blue-700 text-sm font-medium">
                    Perfect for small agencies looking to streamline basic operations
                  </p>
                </div>
              </Card>
            </FadeInSection>

            <FadeInSection delay={400}>
              <Card variant="elevated" className="p-8 h-full border-l-4 border-l-purple-500">
                <div className="flex items-center mb-6">
                  <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center text-purple-600 text-xl mr-4">
                    🚀
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900">Advanced with Make.com</h3>
                </div>
                <ul className="space-y-4 text-gray-700">
                  <li className="flex items-start">
                    <span className="text-green-500 mr-3 mt-1">✓</span>
                    <span><strong>Business integrations:</strong> Connect Calendly, Zoom, Stripe, and communication tools</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-green-500 mr-3 mt-1">✓</span>
                    <span><strong>Automated reporting:</strong> Real-time sales dashboards and custom client reports</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-green-500 mr-3 mt-1">✓</span>
                    <span><strong>Sales optimization:</strong> Automatic lead scoring and follow-up alerts</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-green-500 mr-3 mt-1">✓</span>
                    <span><strong>Advanced analytics:</strong> Sales ROI tracking, client lifecycle analysis, and revenue insights</span>
                  </li>
                </ul>
                <div className="mt-6 p-4 bg-purple-50 rounded-lg">
                  <p className="text-purple-700 text-sm font-medium">
                    Ideal for growing agencies managing complex multi-channel sales processes
                  </p>
                </div>
              </Card>
            </FadeInSection>
          </div>
        </div>
      </section>

      {/* Results & Testimonials */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <DigitalMarketingResults />
        </div>
      </section>

      {/* Trust Elements */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <DigitalMarketingTrust />
        </div>
      </section>

      {/* Final CTA */}
      <section className="py-20 bg-gradient-to-br from-blue-600 via-purple-600 to-pink-700 text-white relative overflow-hidden">
        <div className="absolute inset-0 bg-black opacity-10"></div>

        {/* Decorative elements */}
        <div className="absolute top-10 right-10 w-32 h-32 bg-white opacity-5 rounded-full"></div>
        <div className="absolute bottom-10 left-10 w-24 h-24 bg-white opacity-10 rounded-full"></div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <FadeInSection>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <div>
                <div className="inline-block px-4 py-2 mb-6 bg-blue-500 bg-opacity-20 text-blue-100 rounded-full text-sm font-semibold">
                  🚀 Get Started Today
                </div>
                <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6 leading-tight">
                  Ready to Transform Your <span className="text-blue-200">Digital Marketing Agency</span>?
                </h2>
                <p className="text-xl md:text-2xl text-blue-100 mb-8 leading-relaxed">
                  Get a personalized demo of Kommo CRM configured specifically for digital marketing agencies.
                  See how automation can increase your efficiency by 60% and improve client retention by 40%.
                </p>

                <div className="space-y-4 mb-8">
                  <div className="flex items-center">
                    <span className="text-green-300 mr-3">✓</span>
                    <span>Free consultation and needs assessment</span>
                  </div>
                  <div className="flex items-center">
                    <span className="text-green-300 mr-3">✓</span>
                    <span>Custom automation scenarios for your agency</span>
                  </div>
                  <div className="flex items-center">
                    <span className="text-green-300 mr-3">✓</span>
                    <span>30-day implementation guarantee</span>
                  </div>
                </div>


              </div>
              <div>
                <SolutionsContactForm className="bg-white text-gray-900 shadow-2xl" />
              </div>
            </div>
          </FadeInSection>
        </div>
      </section>
    </div>
  );
}
