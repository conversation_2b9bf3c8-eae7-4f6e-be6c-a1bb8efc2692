import React from 'react';
import { Metadata } from 'next';
import Button from '@/components/Button';
import Card from '@/components/Card';
import ContactForm from '@/components/ContactForm';
import FadeInSection from '@/components/FadeInSection';
import CoachingResults from '@/components/CoachingResults';
import CoachingFunnel from '@/components/CoachingFunnel';
import CoachingTrust from '@/components/CoachingTrust';
import AnimatedCTA from '@/components/AnimatedCTA';
import SolutionsContactForm from '@/components/SolutionsContactForm';

export const metadata: Metadata = {
  title: 'Kommo CRM for Coaches & Consultants | Setmee',
  description: 'Automate client management, lead nurturing, and sales processes for coaches and consultants. Increase client acquisition by 180% and streamline your coaching business.',
  keywords: 'coaching CRM, consultant CRM, client management, coaching automation, lead generation',
  openGraph: {
    title: 'Kommo CRM for Coaches & Consultants | Setmee',
    description: 'Transform your coaching business with automated client management and lead nurturing.',
    type: 'website',
  },
};

export default function CoachingPage() {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative py-20 lg:py-32 bg-gradient-to-br from-green-600 via-emerald-600 to-teal-700 text-white overflow-hidden">
        <div className="absolute inset-0 bg-black opacity-20"></div>
        
        {/* Animated background elements */}
        <div className="absolute top-20 left-10 w-64 h-64 bg-white opacity-5 rounded-full animate-pulse"></div>
        <div className="absolute bottom-20 right-10 w-48 h-48 bg-white opacity-10 rounded-full animate-bounce"></div>
        <div className="absolute top-1/2 left-1/3 w-32 h-32 bg-white opacity-5 rounded-full"></div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            {/* Left Content */}
            <FadeInSection className="text-white">
              <div className="inline-block px-4 py-2 mb-6 bg-green-500 bg-opacity-20 text-green-100 rounded-full text-sm font-semibold">
                🎯 Coaching & Consulting CRM Solution
              </div>
              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 leading-tight">
                Kommo CRM for <span className="text-green-200">Coaches & Consultants</span>
              </h1>
              <p className="text-xl md:text-2xl text-green-100 mb-8 leading-relaxed">
                Automate client acquisition, lead nurturing, and relationship management.
                Increase client conversion by 180% and scale your coaching business efficiently.
                Ready CRM solution for professional coaches.
              </p>

              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-8">
                <div className="flex items-center">
                  <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center mr-3">
                    <span className="text-white text-sm">✓</span>
                  </div>
                  <span className="text-green-100">Automated client onboarding</span>
                </div>
                <div className="flex items-center">
                  <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center mr-3">
                    <span className="text-white text-sm">✓</span>
                  </div>
                  <span className="text-green-100">Lead scoring & qualification</span>
                </div>
                <div className="flex items-center">
                  <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center mr-3">
                    <span className="text-white text-sm">✓</span>
                  </div>
                  <span className="text-green-100">Follow-up automation</span>
                </div>
                <div className="flex items-center">
                  <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center mr-3">
                    <span className="text-white text-sm">✓</span>
                  </div>
                  <span className="text-green-100">Revenue optimization</span>
                </div>
              </div>

              <div className="flex flex-col sm:flex-row gap-4 justify-start items-center mb-8">
                <AnimatedCTA
                  variant="secondary"
                  size="lg"
                  className="shadow-xl px-8 py-4 bg-white text-gray-800 hover:bg-gray-100 hover:text-gray-900 hover:shadow-2xl transition-all duration-300 border-0"
                  shakeDelay={10000}
                >
                  Get Demo Access
                </AnimatedCTA>
                <AnimatedCTA
                  variant="outline"
                  size="lg"
                  className="bg-transparent border-2 border-white text-white font-bold px-8 py-4 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-white hover:bg-white hover:text-green-700 hover:border-green-700"
                  shakeDelay={15000}
                  disableCustomStyle={true}
                >
                  Calculate ROI
                </AnimatedCTA>
              </div>
            </FadeInSection>

            {/* Right Content - Coaching CRM Dashboard Preview */}
            <FadeInSection delay={300} className="hidden lg:block">
              <div className="relative">
                <div className="bg-white bg-opacity-10 backdrop-blur-sm rounded-2xl p-8 border border-white border-opacity-20">
                  <div className="text-center text-white">
                    <div className="text-6xl mb-4">🎯</div>
                    <h3 className="text-xl font-semibold mb-2">Coaching CRM Dashboard</h3>
                    <p className="text-green-100 text-sm">
                      Client pipeline, lead management, and coaching business analytics in one place
                    </p>
                  </div>
                </div>

                {/* Floating elements */}
                <div className="absolute -top-4 -right-4 bg-green-500 text-white px-3 py-1 rounded-full text-sm font-semibold">
                  Live Demo Available
                </div>
                <div className="absolute -bottom-4 -left-4 bg-emerald-500 text-white px-3 py-1 rounded-full text-sm font-semibold">
                  Free Setup
                </div>
              </div>
            </FadeInSection>
          </div>
        </div>
      </section>

      {/* Problems Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <FadeInSection>
            <div className="text-center mb-16">
              <div className="inline-block px-4 py-2 mb-6 bg-red-100 text-red-700 rounded-full text-sm font-semibold">
                ⚠️ Common Coaching Challenges
              </div>
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                Problems Coaches & Consultants Face Daily
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Without proper CRM automation, coaches struggle with lead management,
                client acquisition, and scaling their business
              </p>
            </div>
          </FadeInSection>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <FadeInSection delay={200}>
              <Card variant="outlined" className="text-center p-6 h-full hover:shadow-lg transition-shadow duration-300 border-red-200">
                <div className="text-4xl mb-4">📉</div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Low Conversion Rates</h3>
                <p className="text-gray-600 text-sm">Leads lose interest without proper follow-up and nurturing</p>
                <div className="mt-4 text-green-600 text-sm font-medium">
                  → Automated lead nurturing
                </div>
              </Card>
            </FadeInSection>

            <FadeInSection delay={350}>
              <Card variant="outlined" className="text-center p-6 h-full hover:shadow-lg transition-shadow duration-300 border-red-200">
                <div className="text-4xl mb-4">📊</div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Manual Lead Management</h3>
                <p className="text-gray-600 text-sm">Tracking leads and clients manually takes hours each week</p>
                <div className="mt-4 text-green-600 text-sm font-medium">
                  → Automated lead tracking
                </div>
              </Card>
            </FadeInSection>

            <FadeInSection delay={500}>
              <Card variant="outlined" className="text-center p-6 h-full hover:shadow-lg transition-shadow duration-300 border-red-200">
                <div className="text-4xl mb-4">📞</div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Inconsistent Follow-up</h3>
                <p className="text-gray-600 text-sm">Missing follow-ups with prospects and existing clients</p>
                <div className="mt-4 text-green-600 text-sm font-medium">
                  → Automated communication flows
                </div>
              </Card>
            </FadeInSection>

            <FadeInSection delay={650}>
              <Card variant="outlined" className="text-center p-6 h-full hover:shadow-lg transition-shadow duration-300 border-red-200">
                <div className="text-4xl mb-4">⚖️</div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Scaling Challenges</h3>
                <p className="text-gray-600 text-sm">Can't serve more clients without sacrificing quality</p>
                <div className="mt-4 text-green-600 text-sm font-medium">
                  → Scalable automation systems
                </div>
              </Card>
            </FadeInSection>
          </div>
        </div>
      </section>

      {/* Coaching Sales Funnel */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <CoachingFunnel />
        </div>
      </section>

      {/* Automation Scenarios */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <FadeInSection>
            <div className="text-center mb-16">
              <div className="inline-block px-4 py-2 mb-6 bg-green-100 text-green-700 rounded-full text-sm font-semibold">
                🤖 Automation Scenarios
              </div>
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                Coaching & Education Automation with Kommo CRM
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Choose the automation level that fits your coaching business and technical requirements
              </p>
            </div>
          </FadeInSection>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <FadeInSection delay={200}>
              <Card variant="elevated" className="p-8 h-full border-l-4 border-l-green-500">
                <div className="flex items-center mb-6">
                  <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center text-green-600 text-xl mr-4">
                    ⚡
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900">Basic Kommo Automation</h3>
                </div>
                <ul className="space-y-4 text-gray-700">
                  <li className="flex items-start">
                    <span className="text-green-500 mr-3 mt-1">✓</span>
                    <span><strong>Lead capture:</strong> Automatic lead collection from landing pages and lead magnets</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-green-500 mr-3 mt-1">✓</span>
                    <span><strong>Email sequences:</strong> Welcome series and nurturing campaigns for prospects</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-green-500 mr-3 mt-1">✓</span>
                    <span><strong>Appointment booking:</strong> Automated discovery call scheduling and reminders</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-green-500 mr-3 mt-1">✓</span>
                    <span><strong>Client tracking:</strong> Client status monitoring and milestone notifications</span>
                  </li>
                </ul>
                <div className="mt-6 p-4 bg-green-50 rounded-lg">
                  <p className="text-green-700 text-sm font-medium">
                    Perfect for individual coaches and consultants
                  </p>
                </div>
              </Card>
            </FadeInSection>

            <FadeInSection delay={400}>
              <Card variant="elevated" className="p-8 h-full border-l-4 border-l-emerald-500">
                <div className="flex items-center mb-6">
                  <div className="w-12 h-12 bg-emerald-100 rounded-full flex items-center justify-center text-emerald-600 text-xl mr-4">
                    🚀
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900">Advanced with Make.com</h3>
                </div>
                <ul className="space-y-4 text-gray-700">
                  <li className="flex items-start">
                    <span className="text-green-500 mr-3 mt-1">✓</span>
                    <span><strong>Business integrations:</strong> Connect with Calendly, Zoom, Stripe, and other business tools</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-green-500 mr-3 mt-1">✓</span>
                    <span><strong>Smart engagement:</strong> Behavioral triggers based on client interactions and engagement</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-green-500 mr-3 mt-1">✓</span>
                    <span><strong>Advanced analytics:</strong> Detailed client success metrics and coaching business ROI tracking</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-green-500 mr-3 mt-1">✓</span>
                    <span><strong>Payment automation:</strong> Subscription management and upselling workflows</span>
                  </li>
                </ul>
                <div className="mt-6 p-4 bg-emerald-50 rounded-lg">
                  <p className="text-emerald-700 text-sm font-medium">
                    Ideal for growing coaching businesses and consultancies 
                  </p>
                </div>
              </Card>
            </FadeInSection>
          </div>
        </div>
      </section>

      {/* Results & Testimonials */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <CoachingResults />
        </div>
      </section>

      {/* Trust Elements */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <CoachingTrust />
        </div>
      </section>

      {/* Final CTA */}
      <section className="py-20 bg-gradient-to-br from-green-600 via-emerald-600 to-teal-700 text-white relative overflow-hidden">
        <div className="absolute inset-0 bg-black opacity-10"></div>

        {/* Decorative elements */}
        <div className="absolute top-10 right-10 w-32 h-32 bg-white opacity-5 rounded-full"></div>
        <div className="absolute bottom-10 left-10 w-24 h-24 bg-white opacity-10 rounded-full"></div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <FadeInSection>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <div>
                <div className="inline-block px-4 py-2 mb-6 bg-green-500 bg-opacity-20 text-green-100 rounded-full text-sm font-semibold">
                  🚀 Get Started Today
                </div>
                <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6 leading-tight">
                  Ready to Transform Your <span className="text-green-200">Coaching Business</span>?
                </h2>
                <p className="text-xl md:text-2xl text-green-100 mb-8 leading-relaxed">
                  Get a personalized demo of Kommo CRM configured specifically for coaches and consultants.
                  See how automation can increase client conversion by 180% and scale your business efficiently.
                </p>

                <div className="space-y-4 mb-8">
                  <div className="flex items-center">
                    <span className="text-green-300 mr-3">✓</span>
                    <span>Free consultation and business assessment</span>
                  </div>
                  <div className="flex items-center">
                    <span className="text-green-300 mr-3">✓</span>
                    <span>Custom automation workflows for your niche</span>
                  </div>
                  <div className="flex items-center">
                    <span className="text-green-300 mr-3">✓</span>
                    <span>30-day implementation with ongoing support</span>
                  </div>
                </div>


              </div>
              <div>
                <SolutionsContactForm className="bg-white text-gray-900 shadow-2xl" />
              </div>
            </div>
          </FadeInSection>
        </div>
      </section>
    </div>
  );
}
