import React from 'react';
import { Metadata } from 'next';
import Button from '@/components/Button';
import Card from '@/components/Card';
import ContactForm from '@/components/ContactForm';
import FadeInSection from '@/components/FadeInSection';
import NicheResults from '@/components/NicheResults';
import NicheFunnel from '@/components/NicheFunnel';
import NicheTrust from '@/components/NicheTrust';
import AnimatedCTA from '@/components/AnimatedCTA';
import SolutionsContactForm from '@/components/SolutionsContactForm';

export const metadata: Metadata = {
  title: 'Kommo CRM for Niche eCommerce | Setmee',
  description: 'Automate specialized market customer journeys and niche product sales. Increase conversion rates by 150% and customer retention by 80% with targeted CRM automation.',
  keywords: 'niche eCommerce CRM, specialized market automation, niche product sales, boutique eCommerce, specialty retail CRM',
  openGraph: {
    title: 'Kommo CRM for Niche eCommerce | Setmee',
    description: 'Transform your niche eCommerce business with automated sales processes designed for specialized markets and unique customer journeys.',
    type: 'website',
  },
};

export default function NicheEcommercePage() {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative py-20 lg:py-32 bg-gradient-to-br from-emerald-600 via-teal-600 to-cyan-700 text-white overflow-hidden">
        <div className="absolute inset-0 bg-black opacity-20"></div>
        
        {/* Animated background elements */}
        <div className="absolute top-20 left-10 w-64 h-64 bg-white opacity-5 rounded-full animate-pulse"></div>
        <div className="absolute bottom-20 right-10 w-48 h-48 bg-white opacity-10 rounded-full animate-bounce"></div>
        <div className="absolute top-1/2 left-1/3 w-32 h-32 bg-white opacity-5 rounded-full"></div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            {/* Left Content */}
            <FadeInSection className="text-white">
              <div className="inline-block px-4 py-2 mb-6 bg-emerald-500 bg-opacity-20 text-emerald-100 rounded-full text-sm font-semibold">
                🎯 Niche eCommerce CRM Solution
              </div>
              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 leading-tight">
                Kommo CRM for <span className="text-emerald-200">Niche eCommerce</span>
              </h1>
              <p className="text-xl md:text-2xl text-emerald-100 mb-8 leading-relaxed">
                Automate specialized market customer journeys and unique product sales. 
                Increase conversion rates by 150% and customer retention by 80%. 
                Perfect solution for boutique and specialty retailers.
              </p>

              <div className="flex flex-col sm:flex-row gap-4 justify-start items-center mb-8">
                <AnimatedCTA
                  variant="secondary"
                  size="lg"
                  className="shadow-xl px-8 py-4 bg-white text-gray-800 hover:bg-gray-100 hover:text-gray-900 hover:shadow-2xl transition-all duration-300 border-0"
                  shakeDelay={10000}
                >
                  Get Free CRM Consultation
                </AnimatedCTA>
                <AnimatedCTA
                  variant="outline"
                  size="lg"
                  className="bg-transparent border-2 border-white text-white font-bold px-8 py-4 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-white hover:bg-white hover:text-emerald-600 hover:border-emerald-600"
                  shakeDelay={15000}
                  disableCustomStyle={true}
                >
                  View Success Stories
                </AnimatedCTA>
              </div>

              <div className="flex items-center space-x-6 text-emerald-100">
                <div className="flex items-center">
                  <span className="text-2xl mr-2">⚡</span>
                  <span className="text-sm">Quick Setup</span>
                </div>
                <div className="flex items-center">
                  <span className="text-2xl mr-2">🎯</span>
                  <span className="text-sm">Niche-Focused</span>
                </div>
                <div className="flex items-center">
                  <span className="text-2xl mr-2">📈</span>
                  <span className="text-sm">Proven Results</span>
                </div>
              </div>
            </FadeInSection>

            {/* Right Content - Image */}
            <FadeInSection delay={300} className="relative">
              <div className="relative z-10 bg-white bg-opacity-10 backdrop-blur-sm rounded-2xl p-8 border border-white border-opacity-20">
                <div className="text-center">
                  <div className="text-6xl mb-4">🛍️</div>
                  <h3 className="text-2xl font-bold text-white mb-4">Specialized Market Focus</h3>
                  <div className="grid grid-cols-2 gap-4 text-emerald-100">
                    <div className="text-center">
                      <div className="text-3xl mb-2">🎨</div>
                      <p className="text-sm">Artisan Products</p>
                    </div>
                    <div className="text-center">
                      <div className="text-3xl mb-2">🌱</div>
                      <p className="text-sm">Organic & Natural</p>
                    </div>
                    <div className="text-center">
                      <div className="text-3xl mb-2">🏺</div>
                      <p className="text-sm">Collectibles</p>
                    </div>
                    <div className="text-center">
                      <div className="text-3xl mb-2">💎</div>
                      <p className="text-sm">Luxury Items</p>
                    </div>
                  </div>
                </div>
              </div>
            </FadeInSection>
          </div>
        </div>
      </section>

      {/* Problems Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <FadeInSection>
            <div className="text-center mb-16">
              <div className="inline-block px-4 py-2 mb-6 bg-red-100 text-red-700 rounded-full text-sm font-semibold">
                ⚠️ Niche eCommerce Challenges
              </div>
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                Unique Challenges in Niche eCommerce
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Niche eCommerce businesses face specialized challenges that generic CRM solutions can't address effectively
              </p>
            </div>
          </FadeInSection>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <FadeInSection delay={200}>
              <Card variant="outlined" className="text-center p-6 h-full hover:shadow-lg transition-shadow duration-300 border-red-200">
                <div className="text-4xl mb-4">👥</div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Small Target Audience</h3>
                <p className="text-gray-600 text-sm">Limited customer base requiring precise targeting</p>
                <div className="mt-4 text-emerald-600 text-sm font-medium">
                  → Precise segmentation
                </div>
              </Card>
            </FadeInSection>

            <FadeInSection delay={350}>
              <Card variant="outlined" className="text-center p-6 h-full hover:shadow-lg transition-shadow duration-300 border-red-200">
                <div className="text-4xl mb-4">🎓</div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Customer Education</h3>
                <p className="text-gray-600 text-sm">Need to educate customers about specialized products</p>
                <div className="mt-4 text-emerald-600 text-sm font-medium">
                  → Educational automation
                </div>
              </Card>
            </FadeInSection>

            <FadeInSection delay={500}>
              <Card variant="outlined" className="text-center p-6 h-full hover:shadow-lg transition-shadow duration-300 border-red-200">
                <div className="text-4xl mb-4">💰</div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Higher Price Points</h3>
                <p className="text-gray-600 text-sm">Longer consideration periods for premium products</p>
                <div className="mt-4 text-emerald-600 text-sm font-medium">
                  → Trust building sequences
                </div>
              </Card>
            </FadeInSection>

            <FadeInSection delay={650}>
              <Card variant="outlined" className="text-center p-6 h-full hover:shadow-lg transition-shadow duration-300 border-red-200">
                <div className="text-4xl mb-4">🔄</div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Customer Retention</h3>
                <p className="text-gray-600 text-sm">Building loyalty in competitive niche markets</p>
                <div className="mt-4 text-emerald-600 text-sm font-medium">
                  → Loyalty automation
                </div>
              </Card>
            </FadeInSection>
          </div>
        </div>
      </section>

      {/* Niche Sales Funnel */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <NicheFunnel />
        </div>
      </section>

      {/* Results Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <NicheResults />
        </div>
      </section>

      {/* Automation Scenarios Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <FadeInSection>
            <div className="text-center mb-16">
              <div className="inline-block px-4 py-2 mb-6 bg-emerald-100 text-emerald-700 rounded-full text-sm font-semibold">
                🤖 Automation Scenarios
              </div>
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                Ready Niche eCommerce Automation Solutions
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Choose the automation level that fits your niche business needs and technical requirements
              </p>
            </div>
          </FadeInSection>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* Basic CRM Automation */}
            <FadeInSection delay={200}>
              <Card className="p-8 h-full bg-white border border-gray-200 hover:shadow-lg transition-shadow duration-300">
                <div className="flex items-center mb-6">
                  <div className="w-12 h-12 bg-emerald-100 rounded-full flex items-center justify-center mr-4">
                    <span className="text-2xl">⚡</span>
                  </div>
                  <h3 className="text-2xl font-bold text-gray-900">Basic CRM Automation</h3>
                </div>

                <ul className="space-y-4 mb-8">
                  <li className="flex items-start">
                    <span className="text-emerald-500 mr-3 mt-1">✓</span>
                    <div>
                      <strong className="text-gray-900">Customer segmentation:</strong>
                      <span className="text-gray-600"> Automated niche audience categorization and targeting</span>
                    </div>
                  </li>
                  <li className="flex items-start">
                    <span className="text-emerald-500 mr-3 mt-1">✓</span>
                    <div>
                      <strong className="text-gray-900">Educational sequences:</strong>
                      <span className="text-gray-600"> Automated product education and trust building campaigns</span>
                    </div>
                  </li>
                  <li className="flex items-start">
                    <span className="text-emerald-500 mr-3 mt-1">✓</span>
                    <div>
                      <strong className="text-gray-900">Purchase follow-up:</strong>
                      <span className="text-gray-600"> Automated post-purchase communication and loyalty building</span>
                    </div>
                  </li>
                  <li className="flex items-start">
                    <span className="text-emerald-500 mr-3 mt-1">✓</span>
                    <div>
                      <strong className="text-gray-900">Basic reporting:</strong>
                      <span className="text-gray-600"> Customer journey tracking and conversion analytics</span>
                    </div>
                  </li>
                </ul>

                <p className="text-gray-600 text-sm">
                  <strong>Perfect for:</strong> Small to medium niche retailers getting started with CRM automation
                </p>
              </Card>
            </FadeInSection>

            {/* Advanced with Make.com */}
            <FadeInSection delay={400}>
              <Card className="p-8 h-full bg-white border border-gray-200 hover:shadow-lg transition-shadow duration-300">
                <div className="flex items-center mb-6">
                  <div className="w-12 h-12 bg-emerald-100 rounded-full flex items-center justify-center mr-4">
                    <span className="text-2xl">🚀</span>
                  </div>
                  <h3 className="text-2xl font-bold text-gray-900">Advanced with Make.com</h3>
                </div>

                <ul className="space-y-4 mb-8">
                  <li className="flex items-start">
                    <span className="text-emerald-500 mr-3 mt-1">✓</span>
                    <div>
                      <strong className="text-gray-900">Multi-platform integration:</strong>
                      <span className="text-gray-600"> Connect with eCommerce platforms, payment systems, and analytics tools automatically</span>
                    </div>
                  </li>
                  <li className="flex items-start">
                    <span className="text-emerald-500 mr-3 mt-1">✓</span>
                    <div>
                      <strong className="text-gray-900">Smart personalization:</strong>
                      <span className="text-gray-600"> AI-driven product recommendations and personalized customer experiences</span>
                    </div>
                  </li>
                  <li className="flex items-start">
                    <span className="text-emerald-500 mr-3 mt-1">✓</span>
                    <div>
                      <strong className="text-gray-900">Community automation:</strong>
                      <span className="text-gray-600"> Automated VIP programs, referral systems, and community engagement</span>
                    </div>
                  </li>
                  <li className="flex items-start">
                    <span className="text-emerald-500 mr-3 mt-1">✓</span>
                    <div>
                      <strong className="text-gray-900">Advanced analytics:</strong>
                      <span className="text-gray-600"> Customer lifetime value, niche market insights, and predictive analytics</span>
                    </div>
                  </li>
                </ul>

                <p className="text-gray-600 text-sm">
                  <strong>Perfect for:</strong> Established niche retailers wanting maximum automation and customer experience optimization
                </p>
              </Card>
            </FadeInSection>
          </div>
        </div>
      </section>

      {/* Trust Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <NicheTrust />
        </div>
      </section>

      {/* Final CTA Section */}
      <section className="py-20 bg-gradient-to-br from-emerald-600 via-teal-600 to-cyan-700 text-white relative overflow-hidden">
        <div className="absolute inset-0 bg-black opacity-10"></div>
        
        {/* Decorative elements */}
        <div className="absolute top-10 right-10 w-32 h-32 bg-white opacity-5 rounded-full"></div>
        <div className="absolute bottom-10 left-10 w-24 h-24 bg-white opacity-10 rounded-full"></div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <FadeInSection>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <div>
                <div className="inline-block px-4 py-2 mb-6 bg-emerald-500 bg-opacity-20 text-emerald-100 rounded-full text-sm font-semibold">
                  🚀 Niche eCommerce Ready
                </div>
                <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6 leading-tight">
                  Ready to Grow Your <span className="text-emerald-200">Niche Business</span>?
                </h2>
                <p className="text-xl text-emerald-100 mb-8 leading-relaxed">
                  Get a personalized demo of Kommo CRM configured for your unique niche market.
                  See how specialized automation can transform your eCommerce operations.
                </p>

                <div className="space-y-4 mb-8">
                  <div className="flex items-center">
                    <span className="text-emerald-300 mr-3">✓</span>
                    <span>Free niche market consultation and needs assessment</span>
                  </div>
                  <div className="flex items-center">
                    <span className="text-emerald-300 mr-3">✓</span>
                    <span>Custom automation for your unique product lines</span>
                  </div>
                  <div className="flex items-center">
                    <span className="text-emerald-300 mr-3">✓</span>
                    <span>Specialized implementation with dedicated support</span>
                  </div>
                </div>


              </div>
              <div>
                <SolutionsContactForm className="bg-white text-gray-900 shadow-2xl" />
              </div>
            </div>
          </FadeInSection>
        </div>
      </section>
    </div>
  );
}
