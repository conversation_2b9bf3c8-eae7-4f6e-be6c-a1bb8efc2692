import React from 'react';
import type { Metadata } from 'next';
import FadeInSection from '@/components/FadeInSection';
import AnimatedCTA from '@/components/AnimatedCTA';
import EducationFunnel from '@/components/EducationFunnel';
import EducationResults from '@/components/EducationResults';
import EducationTrust from '@/components/EducationTrust';
import SolutionsContactForm from '@/components/SolutionsContactForm';

export const metadata: Metadata = {
  title: 'Kommo CRM for Education & Language Schools | Student Management | Setmee',
  description: 'Specialized Kommo CRM solution for educational institutions and language schools. Automate student enrollment, lesson scheduling, and communication. Increase enrollment by 35% and reduce admin tasks by 50%.',
  keywords: 'education CRM, language school CRM, student management system, educational automation, lesson booking CRM, Kommo education',
  openGraph: {
    title: 'Kommo CRM for Education & Language Schools | Student Management | Setmee',
    description: 'Specialized Kommo CRM solution for educational institutions and language schools. Automate student enrollment, lesson scheduling, and communication.',
    type: 'website',
  },
};

export default function EducationPage() {
  return (
    <main className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="relative py-20 bg-gradient-to-br from-blue-600 to-purple-700 text-white overflow-hidden">
        <div className="absolute inset-0 bg-black/10"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <FadeInSection className="text-left">
              <div className="inline-block px-4 py-2 mb-6 bg-blue-100 text-blue-600 rounded-full text-sm font-semibold">
                📚 Education CRM Solution
              </div>
              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 leading-tight">
                Kommo CRM for{' '}
                <span className="bg-gradient-to-r from-yellow-300 to-orange-300 bg-clip-text text-transparent">
                  Education & Language Schools
                </span>
              </h1>
              <p className="text-xl md:text-2xl mb-8 text-blue-100 leading-relaxed">
                Automate student enrollment, lesson scheduling, and communication. Increase enrollment by 35% and reduce administrative tasks by 50%. Ready CRM solution for educational institutions.
              </p>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
                <div className="flex items-center">
                  <div className="w-6 h-6 bg-green-400 rounded-full flex items-center justify-center mr-3 flex-shrink-0">
                    <span className="text-green-800 text-sm font-bold">✓</span>
                  </div>
                  <span className="text-blue-100">+35% student enrollment</span>
                </div>
                <div className="flex items-center">
                  <div className="w-6 h-6 bg-green-400 rounded-full flex items-center justify-center mr-3 flex-shrink-0">
                    <span className="text-green-800 text-sm font-bold">✓</span>
                  </div>
                  <span className="text-blue-100">50% less admin tasks</span>
                </div>
                <div className="flex items-center">
                  <div className="w-6 h-6 bg-green-400 rounded-full flex items-center justify-center mr-3 flex-shrink-0">
                    <span className="text-green-800 text-sm font-bold">✓</span>
                  </div>
                  <span className="text-blue-100">Automated lesson scheduling</span>
                </div>
                <div className="flex items-center">
                  <div className="w-6 h-6 bg-green-400 rounded-full flex items-center justify-center mr-3 flex-shrink-0">
                    <span className="text-green-800 text-sm font-bold">✓</span>
                  </div>
                  <span className="text-blue-100">Student communication automation</span>
                </div>
              </div>

              <div className="flex flex-col sm:flex-row gap-4">
                <AnimatedCTA
                  variant="secondary"
                  size="lg"
                  className="shadow-xl px-8 py-4 bg-white text-blue-600 hover:bg-blue-50 hover:text-blue-700 hover:shadow-2xl transition-all duration-300 border-0"
                  shakeDelay={10000}
                  href="#contact"
                >
                  Get Free Demo
                </AnimatedCTA>
                <AnimatedCTA
                  variant="outline"
                  size="lg"
                  className="!bg-transparent !border-2 !border-white !text-white font-bold !px-8 !py-4 !transition-all !duration-300 focus:!outline-none focus:!ring-2 focus:!ring-white hover:!bg-white hover:!text-blue-600 hover:!border-blue-600 !rounded-lg !text-lg"
                  shakeDelay={15000}
                  disableCustomStyle={true}
                  href="/roi-calculator/"
                >
                  Calculate ROI
                </AnimatedCTA>
              </div>
            </FadeInSection>

            <FadeInSection delay={300} className="relative">
              <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20">
                <div className="flex items-center mb-6">
                  <div className="w-12 h-12 bg-blue-500 rounded-lg flex items-center justify-center mr-4">
                    <span className="text-2xl">📊</span>
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold text-white">Education Dashboard</h3>
                    <p className="text-blue-200">Student pipeline, lesson management, and enrollment tracking in one place</p>
                  </div>
                </div>
                <div className="space-y-4">
                  <div className="flex justify-between items-center p-3 bg-white/10 rounded-lg">
                    <span className="text-blue-100">Live Updates</span>
                    <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
                  </div>
                  <div className="flex justify-between items-center p-3 bg-white/10 rounded-lg">
                    <span className="text-blue-100">Real-time Analytics</span>
                    <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
                  </div>
                </div>
              </div>
            </FadeInSection>
          </div>
        </div>
      </section>

      {/* Problems Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <FadeInSection className="text-center mb-16">
            <div className="inline-block px-4 py-2 mb-6 bg-blue-100 text-blue-600 rounded-full text-sm font-semibold">
              🎯 Common Challenges
            </div>
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              What Education Problems Does Kommo CRM Solve?
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Address the most common pain points in educational institution management and student communication
            </p>
          </FadeInSection>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <FadeInSection delay={200}>
              <div className="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-shadow duration-300 border border-gray-100">
                <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mb-4">
                  <span className="text-2xl">📋</span>
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">Manual Student Tracking</h3>
                <p className="text-gray-600 mb-4">Managing student inquiries and enrollment across spreadsheets and paper forms</p>
                <div className="text-blue-600 font-medium">→ Automated student pipeline</div>
              </div>
            </FadeInSection>

            <FadeInSection delay={400}>
              <div className="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-shadow duration-300 border border-gray-100">
                <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mb-4">
                  <span className="text-2xl">📞</span>
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">Lost Student Communication</h3>
                <p className="text-gray-600 mb-4">Missing follow-ups with prospective students and inconsistent communication</p>
                <div className="text-blue-600 font-medium">→ Automated student communication</div>
              </div>
            </FadeInSection>

            <FadeInSection delay={600}>
              <div className="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-shadow duration-300 border border-gray-100">
                <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mb-4">
                  <span className="text-2xl">⏰</span>
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">Scheduling Conflicts</h3>
                <p className="text-gray-600 mb-4">Coordinating trial lessons, consultations, and class schedules manually</p>
                <div className="text-blue-600 font-medium">→ Automated scheduling system</div>
              </div>
            </FadeInSection>

            <FadeInSection delay={800}>
              <div className="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-shadow duration-300 border border-gray-100">
                <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mb-4">
                  <span className="text-2xl">📊</span>
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">No Enrollment Insights</h3>
                <p className="text-gray-600 mb-4">Lack of data on enrollment conversion rates and student lifecycle</p>
                <div className="text-blue-600 font-medium">→ Real-time analytics dashboard</div>
              </div>
            </FadeInSection>
          </div>
        </div>
      </section>

      {/* Funnel Section */}
      <EducationFunnel />

      {/* Results Section */}
      <EducationResults />

      {/* Automation Scenarios */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <FadeInSection className="text-center mb-16">
            <div className="inline-block px-4 py-2 mb-6 bg-blue-100 text-blue-600 rounded-full text-sm font-semibold">
              🤖 Automation Scenarios
            </div>
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Ready Education Automation Solutions
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Choose the automation level that fits your educational institution needs and technical requirements
            </p>
          </FadeInSection>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <FadeInSection delay={200}>
              <div className="bg-white rounded-xl p-8 shadow-lg border border-gray-100 hover:shadow-xl transition-shadow duration-300">
                <div className="flex items-center mb-6">
                  <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4">
                    <span className="text-2xl">⚡</span>
                  </div>
                  <h3 className="text-2xl font-bold text-gray-900">Basic CRM Automation</h3>
                </div>
                <ul className="space-y-4 mb-6">
                  <li className="flex items-start">
                    <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mr-3 mt-0.5 flex-shrink-0">
                      <span className="text-green-600 text-sm font-bold">✓</span>
                    </div>
                    <div>
                      <strong className="text-gray-900">Student pipeline management:</strong>
                      <span className="text-gray-600"> Automated enrollment tracking and status updates</span>
                    </div>
                  </li>
                  <li className="flex items-start">
                    <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mr-3 mt-0.5 flex-shrink-0">
                      <span className="text-green-600 text-sm font-bold">✓</span>
                    </div>
                    <div>
                      <strong className="text-gray-900">Student communication:</strong>
                      <span className="text-gray-600"> Automated follow-up sequences and lesson reminders</span>
                    </div>
                  </li>
                  <li className="flex items-start">
                    <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mr-3 mt-0.5 flex-shrink-0">
                      <span className="text-green-600 text-sm font-bold">✓</span>
                    </div>
                    <div>
                      <strong className="text-gray-900">Trial lesson booking:</strong>
                      <span className="text-gray-600"> Automatic scheduling and confirmation notifications</span>
                    </div>
                  </li>
                  <li className="flex items-start">
                    <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mr-3 mt-0.5 flex-shrink-0">
                      <span className="text-green-600 text-sm font-bold">✓</span>
                    </div>
                    <div>
                      <strong className="text-gray-900">Basic reporting:</strong>
                      <span className="text-gray-600"> Enrollment tracking and teacher performance metrics</span>
                    </div>
                  </li>
                </ul>
                <p className="text-gray-600 italic">
                  Perfect for: Small to medium educational institutions getting started with automation
                </p>
              </div>
            </FadeInSection>

            <FadeInSection delay={400}>
              <div className="bg-white rounded-xl p-8 shadow-lg border border-gray-100 hover:shadow-xl transition-shadow duration-300">
                <div className="flex items-center mb-6">
                  <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mr-4">
                    <span className="text-2xl">🚀</span>
                  </div>
                  <h3 className="text-2xl font-bold text-gray-900">Advanced with Make.com</h3>
                </div>
                <ul className="space-y-4 mb-6">
                  <li className="flex items-start">
                    <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mr-3 mt-0.5 flex-shrink-0">
                      <span className="text-green-600 text-sm font-bold">✓</span>
                    </div>
                    <div>
                      <strong className="text-gray-900">Multi-platform integration:</strong>
                      <span className="text-gray-600"> Connect with Zoom, payment systems, and scheduling tools automatically</span>
                    </div>
                  </li>
                  <li className="flex items-start">
                    <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mr-3 mt-0.5 flex-shrink-0">
                      <span className="text-green-600 text-sm font-bold">✓</span>
                    </div>
                    <div>
                      <strong className="text-gray-900">Smart student qualification:</strong>
                      <span className="text-gray-600"> Automated assessment of student needs and course recommendations</span>
                    </div>
                  </li>
                  <li className="flex items-start">
                    <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mr-3 mt-0.5 flex-shrink-0">
                      <span className="text-green-600 text-sm font-bold">✓</span>
                    </div>
                    <div>
                      <strong className="text-gray-900">Payment automation:</strong>
                      <span className="text-gray-600"> Automatic invoicing, payment tracking, and renewal reminders</span>
                    </div>
                  </li>
                  <li className="flex items-start">
                    <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mr-3 mt-0.5 flex-shrink-0">
                      <span className="text-green-600 text-sm font-bold">✓</span>
                    </div>
                    <div>
                      <strong className="text-gray-900">Advanced analytics:</strong>
                      <span className="text-gray-600"> Student lifetime value, course profitability, and retention analysis</span>
                    </div>
                  </li>
                </ul>
                <p className="text-gray-600 italic">
                  Perfect for: Established educational institutions wanting maximum automation and efficiency
                </p>
              </div>
            </FadeInSection>
          </div>
        </div>
      </section>

      {/* Trust Section */}
      <EducationTrust />

      {/* Final CTA Section */}
      <section id="contact" className="py-20 bg-gradient-to-br from-blue-600 to-purple-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <FadeInSection>
              <div className="inline-block px-4 py-2 mb-6 bg-blue-100 text-blue-600 rounded-full text-sm font-semibold">
                🚀 Get Started Today
              </div>
              <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
                Ready to Transform Your Educational Institution?
              </h2>
              <p className="text-xl text-blue-100 mb-8">
                Join hundreds of educational institutions that have automated their processes with Kommo CRM. Get a personalized demo and see results in the first week.
              </p>
              <div className="space-y-4">
                <div className="flex items-center">
                  <div className="w-6 h-6 bg-green-400 rounded-full flex items-center justify-center mr-3 flex-shrink-0">
                    <span className="text-green-800 text-sm font-bold">✓</span>
                  </div>
                  <span className="text-blue-100">35% increase in student enrollment</span>
                </div>
                <div className="flex items-center">
                  <div className="w-6 h-6 bg-green-400 rounded-full flex items-center justify-center mr-3 flex-shrink-0">
                    <span className="text-green-800 text-sm font-bold">✓</span>
                  </div>
                  <span className="text-blue-100">50% reduction in admin tasks</span>
                </div>
                <div className="flex items-center">
                  <div className="w-6 h-6 bg-green-400 rounded-full flex items-center justify-center mr-3 flex-shrink-0">
                    <span className="text-green-800 text-sm font-bold">✓</span>
                  </div>
                  <span className="text-blue-100">3x faster enrollment process</span>
                </div>
              </div>
            </FadeInSection>

            <FadeInSection delay={300}>
              <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20">
                <SolutionsContactForm />
              </div>
            </FadeInSection>
          </div>
        </div>
      </section>
    </main>
  );
}
