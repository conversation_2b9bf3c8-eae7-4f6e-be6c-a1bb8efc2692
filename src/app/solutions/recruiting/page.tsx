import React from 'react';
import { Metadata } from 'next';
import Button from '@/components/Button';
import Card from '@/components/Card';
import ContactForm from '@/components/ContactForm';
import FadeInSection from '@/components/FadeInSection';
import RecruitingResults from '@/components/RecruitingResults';
import RecruitingFunnel from '@/components/RecruitingFunnel';
import RecruitingTrust from '@/components/RecruitingTrust';
import AnimatedCTA from '@/components/AnimatedCTA';
import SolutionsContactForm from '@/components/SolutionsContactForm';


export const metadata: Metadata = {
  title: 'Kommo CRM for Recruiting Agencies | Automated Candidate Management | Setmee',
  description: 'Transform your recruiting business with Kommo CRM. Automate candidate and client management, increase placements by 45%, reduce routine tasks by 60%. Get demo today.',
  keywords: 'recruiting CRM, automated candidate management, recruitment automation, Kommo CRM recruiting, talent acquisition CRM, recruitment management system',
  openGraph: {
    title: 'Kommo CRM for Recruiting Agencies | Setmee',
    description: 'Automate your recruitment process with Kommo CRM. Proven to increase successful placements by 45% and reduce routine tasks by 60%.',
    type: 'website',
  },
};

export default function RecruitingPage() {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-700 py-20 relative overflow-hidden">
        <div className="absolute inset-0 bg-black opacity-10"></div>

        {/* Decorative elements */}
        <div className="absolute top-10 right-10 w-32 h-32 bg-white opacity-5 rounded-full"></div>
        <div className="absolute bottom-10 left-10 w-24 h-24 bg-white opacity-10 rounded-full"></div>
        <div className="absolute top-1/2 left-1/4 w-16 h-16 bg-white opacity-5 rounded-full"></div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            {/* Left Content */}
            <FadeInSection className="text-white">
              <div className="inline-block px-4 py-2 mb-6 bg-blue-500 bg-opacity-20 text-blue-100 rounded-full text-sm font-semibold">
                👥 Recruiting CRM Solution
              </div>
              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 leading-tight">
                Kommo CRM for <span className="text-blue-200">Recruiting Agencies</span>
              </h1>
              <p className="text-xl md:text-2xl text-blue-100 mb-8 leading-relaxed">
                Automate candidate and client management, increase placements by 45%,
                and reduce routine tasks by 60%. Ready CRM solution for recruitment professionals.
              </p>

              {/* Key Benefits */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
                <div className="flex items-center text-blue-100">
                  <span className="text-green-400 mr-3 text-xl">✓</span>
                  <span>+45% more successful placements</span>
                </div>
                <div className="flex items-center text-blue-100">
                  <span className="text-green-400 mr-3 text-xl">✓</span>
                  <span>60% less routine tasks</span>
                </div>
                <div className="flex items-center text-blue-100">
                  <span className="text-green-400 mr-3 text-xl">✓</span>
                  <span>Automated candidate tracking</span>
                </div>
                <div className="flex items-center text-blue-100">
                  <span className="text-green-400 mr-3 text-xl">✓</span>
                  <span>Client relationship management</span>
                </div>
              </div>

              <div className="flex flex-col sm:flex-row gap-4">
                <AnimatedCTA
                  variant="secondary"
                  size="lg"
                  className="shadow-xl px-8 py-4 bg-white text-blue-600 hover:bg-blue-50 hover:text-blue-700 hover:shadow-2xl transition-all duration-300 border-0"
                  shakeDelay={10000}
                  href="#contact"
                >
                  Get Free Demo
                </AnimatedCTA>
                <AnimatedCTA
                  variant="outline"
                  size="lg"
                  className="!bg-transparent !border-2 !border-white !text-white font-bold !px-8 !py-4 !transition-all !duration-300 focus:!outline-none focus:!ring-2 focus:!ring-white hover:!bg-white hover:!text-blue-600 hover:!border-blue-600 !rounded-lg !text-lg"
                  shakeDelay={15000}
                  disableCustomStyle={true}
                  href="/roi-calculator"
                >
                  Calculate ROI
                </AnimatedCTA>
              </div>
            </FadeInSection>

            {/* Right Content - Dashboard Preview */}
            <FadeInSection delay={300} className="hidden lg:block">
              <div className="relative">
                <div className="bg-white bg-opacity-10 backdrop-blur-sm rounded-2xl p-8 border border-white border-opacity-20">
                  <div className="text-center text-white">
                    <div className="text-6xl mb-4">📊</div>
                    <h3 className="text-xl font-semibold mb-2">Recruiting Dashboard</h3>
                    <p className="text-blue-100 text-sm">
                      Candidate pipeline, client management, and placement tracking in one place
                    </p>
                  </div>
                </div>

                {/* Floating elements */}
                <div className="absolute -top-4 -right-4 bg-green-500 text-white px-3 py-1 rounded-full text-sm font-semibold animate-pulse">
                  Live Updates
                </div>
                <div className="absolute -bottom-4 -left-4 bg-yellow-500 text-white px-3 py-1 rounded-full text-sm font-semibold">
                  Real-time Analytics
                </div>
              </div>
            </FadeInSection>
          </div>
        </div>
      </section>

      {/* Problems Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <FadeInSection className="text-center mb-16">
            <div className="inline-block px-4 py-2 mb-6 bg-blue-100 text-blue-600 rounded-full text-sm font-semibold">
              🎯 Common Challenges
            </div>
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              What Recruiting Problems Does Kommo CRM Solve?
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Address the most common pain points in recruitment and candidate management
            </p>
          </FadeInSection>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <FadeInSection delay={200}>
              <Card variant="outlined" className="text-center p-6 h-full hover:shadow-lg transition-shadow duration-300 border-red-200">
                <div className="text-4xl mb-4">📝</div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Manual Candidate Tracking</h3>
                <p className="text-gray-600 text-sm">Managing candidate information across spreadsheets and emails</p>
                <div className="mt-4 text-green-600 text-sm font-medium">
                  → Automated candidate pipeline
                </div>
              </Card>
            </FadeInSection>

            <FadeInSection delay={350}>
              <Card variant="outlined" className="text-center p-6 h-full hover:shadow-lg transition-shadow duration-300 border-red-200">
                <div className="text-4xl mb-4">📞</div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Lost Client Communication</h3>
                <p className="text-gray-600 text-sm">Missing follow-ups and inconsistent client updates</p>
                <div className="mt-4 text-green-600 text-sm font-medium">
                  → Automated client communication
                </div>
              </Card>
            </FadeInSection>

            <FadeInSection delay={500}>
              <Card variant="outlined" className="text-center p-6 h-full hover:shadow-lg transition-shadow duration-300 border-red-200">
                <div className="text-4xl mb-4">⏰</div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Interview Scheduling Chaos</h3>
                <p className="text-gray-600 text-sm">Coordinating interviews between candidates and clients manually</p>
                <div className="mt-4 text-green-600 text-sm font-medium">
                  → Automated scheduling system
                </div>
              </Card>
            </FadeInSection>

            <FadeInSection delay={650}>
              <Card variant="outlined" className="text-center p-6 h-full hover:shadow-lg transition-shadow duration-300 border-red-200">
                <div className="text-4xl mb-4">📊</div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">No Performance Insights</h3>
                <p className="text-gray-600 text-sm">Lack of data on placement success rates and recruiter performance</p>
                <div className="mt-4 text-green-600 text-sm font-medium">
                  → Real-time analytics dashboard
                </div>
              </Card>
            </FadeInSection>
          </div>
        </div>
      </section>

      {/* Funnel Section */}
      <RecruitingFunnel />

      {/* Results Section */}
      <RecruitingResults />

      {/* Automation Scenarios */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <FadeInSection>
            <div className="text-center mb-16">
              <div className="inline-block px-4 py-2 mb-6 bg-blue-100 text-blue-700 rounded-full text-sm font-semibold">
                🤖 Automation Scenarios
              </div>
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                Ready Recruiting Automation Solutions
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Choose the automation level that fits your recruiting agency needs and technical requirements
              </p>
            </div>
          </FadeInSection>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <FadeInSection delay={200}>
              <Card variant="elevated" className="p-8 h-full border-l-4 border-l-blue-500">
                <div className="flex items-center mb-6">
                  <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 text-xl mr-4">
                    ⚡
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900">Basic CRM Automation</h3>
                </div>
                <ul className="space-y-4 text-gray-700">
                  <li className="flex items-start">
                    <span className="text-green-500 mr-3 mt-1">✓</span>
                    <span><strong>Candidate pipeline management:</strong> Automated status updates and stage progression</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-green-500 mr-3 mt-1">✓</span>
                    <span><strong>Client communication:</strong> Automated follow-up sequences and status updates</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-green-500 mr-3 mt-1">✓</span>
                    <span><strong>Interview reminders:</strong> Automatic notifications for all parties</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-green-500 mr-3 mt-1">✓</span>
                    <span><strong>Basic reporting:</strong> Placement tracking and recruiter performance metrics</span>
                  </li>
                </ul>
                <div className="mt-6 p-4 bg-blue-50 rounded-lg">
                  <p className="text-blue-800 text-sm font-medium">Perfect for: Small to medium recruiting agencies getting started with automation</p>
                </div>
              </Card>
            </FadeInSection>

            <FadeInSection delay={400}>
              <Card variant="elevated" className="p-8 h-full border-l-4 border-l-purple-500">
                <div className="flex items-center mb-6">
                  <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center text-purple-600 text-xl mr-4">
                    🚀
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900">Advanced with Make.com</h3>
                </div>
                <ul className="space-y-4 text-gray-700">
                  <li className="flex items-start">
                    <span className="text-green-500 mr-3 mt-1">✓</span>
                    <span><strong>Multi-platform integration:</strong> Connect with Calendly, Zoom, and document systems automatically</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-green-500 mr-3 mt-1">✓</span>
                    <span><strong>Smart candidate matching:</strong> Automated candidate-job matching with instant notifications</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-green-500 mr-3 mt-1">✓</span>
                    <span><strong>Interview automation:</strong> Automatic scheduling, room booking, and follow-up sequences</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-green-500 mr-3 mt-1">✓</span>
                    <span><strong>Advanced analytics:</strong> ROI tracking, time-to-fill metrics, and performance insights</span>
                  </li>
                </ul>
                <div className="mt-6 p-4 bg-purple-50 rounded-lg">
                  <p className="text-purple-800 text-sm font-medium">Perfect for: Established recruiting agencies wanting maximum automation and efficiency</p>
                </div>
              </Card>
            </FadeInSection>
          </div>
        </div>
      </section>

      {/* Trust Section */}
      <RecruitingTrust />

      {/* Final CTA */}
      <section className="py-20 bg-gradient-to-br from-blue-600 to-purple-600 text-white relative overflow-hidden">
        <div className="absolute inset-0 bg-black opacity-10"></div>

        {/* Decorative elements */}
        <div className="absolute top-10 right-10 w-32 h-32 bg-white opacity-5 rounded-full"></div>
        <div className="absolute bottom-10 left-10 w-24 h-24 bg-white opacity-10 rounded-full"></div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <FadeInSection>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <div>
                <div className="inline-block px-4 py-2 mb-6 bg-blue-500 bg-opacity-20 text-blue-100 rounded-full text-sm font-semibold">
                  🚀 Get Started Today
                </div>
                <h2 className="text-3xl md:text-4xl font-bold mb-6">
                  Ready to Transform Your Recruiting Process?
                </h2>
                <p className="text-xl text-blue-100 mb-8">
                  Join hundreds of recruiting agencies that have automated their processes with Kommo CRM. Get a personalized demo and see results in the first week.
                </p>

                <div className="space-y-4 mb-8">
                  <div className="flex items-center">
                    <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center mr-3">
                      <span className="text-white text-sm">✓</span>
                    </div>
                    <span>45% more successful placements</span>
                  </div>
                  <div className="flex items-center">
                    <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center mr-3">
                      <span className="text-white text-sm">✓</span>
                    </div>
                    <span>60% reduction in routine tasks</span>
                  </div>
                  <div className="flex items-center">
                    <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center mr-3">
                      <span className="text-white text-sm">✓</span>
                    </div>
                    <span>3x faster time-to-fill</span>
                  </div>
                </div>

                <div className="flex flex-col sm:flex-row gap-4">
                  <Button
                    variant="secondary"
                    size="lg"
                    className="bg-white text-blue-600 hover:bg-gray-100"
                  >
                    Download Case Study
                  </Button>
                </div>
              </div>
              <div>
                <SolutionsContactForm className="bg-white text-gray-900 shadow-2xl" />
              </div>
            </div>
          </FadeInSection>
        </div>
      </section>
    </div>
  );
}
